version = 1
SPDX-PackageName = "treeland"
SPDX-PackageDownloadLocation = "https://github.com/linuxdeepin/treeland"

[[annotations]]
path = [".github/**", ".obs/**.yml", "garnix.yaml"]
precedence = "aggregate"
SPDX-FileCopyrightText = "None"
SPDX-License-Identifier = "CC0-1.0"

[[annotations]]
path = [".clang-format", ".editorconfig", "**.frag", "**.ui", "**.vert", "**/qmldir"]
precedence = "aggregate"
SPDX-FileCopyrightText = "None"
SPDX-License-Identifier = "CC0-1.0"

[[annotations]]
path = ["debian/**", "rpm/**", "archlinux/**"]
precedence = "aggregate"
SPDX-FileCopyrightText = "None"
SPDX-License-Identifier = "CC0-1.0"

[[annotations]]
path = ["**.nix", ".envrc", "flake.lock", "renovate.json"]
precedence = "aggregate"
SPDX-FileCopyrightText = "None"
SPDX-License-Identifier = "CC0-1.0"

[[annotations]]
path = ["translations/**", "po/**", "**.qm", "**.ts"]
precedence = "aggregate"
SPDX-FileCopyrightText = "UnionTech Software Technology Co., Ltd."
SPDX-License-Identifier = "GPL-2.0-only"

[[annotations]]
path = ["**.svg", "**.png", "**.jpg", "**.jpeg", "**.webp", "**.dci", "**.gif"]
precedence = "aggregate"
SPDX-FileCopyrightText = "UnionTech Software Technology Co., Ltd."
SPDX-License-Identifier = "CC0-1.0"

[[annotations]]
path = ["REUSE.toml", "**.json", "**.service", "**.txt", "**.ini", "**.sh", "**.html", ".gitignore", ".gitmodules", "INSTALL.md", "**.desktop", "**.css", "**.ttf", "**.conf", "**.xml", ".release.json"]
precedence = "aggregate"
SPDX-FileCopyrightText = "UnionTech Software Technology Co., Ltd."
SPDX-License-Identifier = "CC0-1.0"

[[annotations]]
path = ["**.md", "README.md", "README.zh_CN.md", "CONTRIBUTING.md", "ChangeLog", "CONTRIBUTORS"]
precedence = "aggregate"
SPDX-FileCopyrightText = "UnionTech Software Technology Co., Ltd."
SPDX-License-Identifier = "CC-BY-4.0"

[[annotations]]
path = ["**.cmake", "CMakeLists.txt", "**.in", "**.qrc", "misc/**", "services/**", "releng/create_changelog.pl"]
precedence = "aggregate"
SPDX-FileCopyrightText = "None"
SPDX-License-Identifier = "GPL-2.0-only"

[[annotations]]
path = ["src/greeter/**.h", "src/greeter/**.cpp"]
precedence = "aggregate"
SPDX-FileCopyrightText = "None"
SPDX-License-Identifier = "GPL-2.0-only"
