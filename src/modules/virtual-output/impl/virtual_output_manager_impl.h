// Copyright (C) 2024 Lu YaNing <<EMAIL>>.
// SPDX-License-Identifier: Apache-2.0 OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only

#pragma once

#include "treeland-virtual-output-manager-protocol.h"

#include <wayland-server-core.h>

#include <qwdisplay.h>
#include <qwsignalconnector.h>

#include <QList>
#include <QObject>
#include <QString>

struct treeland_virtual_output_v1;

struct treeland_virtual_output_manager_v1 : public QObject
{
    Q_OBJECT
public:
    ~treeland_virtual_output_manager_v1();

    wl_global *global{ nullptr };
    wl_list resources;
    wl_event_loop *event_loop{ nullptr };
    QList<treeland_virtual_output_v1 *> virtual_output;

    static treeland_virtual_output_manager_v1 *create(QW_NAMESPACE::qw_display *display);

Q_SIGNALS:
    void virtualOutputCreated(treeland_virtual_output_v1 *virtual_output);
    void virtualOutputDestroy(treeland_virtual_output_v1 *virtual_output);
    void before_destroy();
};

struct treeland_virtual_output_v1 : public QObject
{
    Q_OBJECT
public:
    ~treeland_virtual_output_v1();
    treeland_virtual_output_manager_v1 *manager{ nullptr };
    wl_resource *resource{ nullptr };
    QString name; // Client-defined group name
    struct wl_array *screen_outputs;

    QStringList outputList;

    void send_outputs(QString name, struct wl_array *outputs);
    void send_error(uint32_t code,
                    const char *message); // tode: send err code and message

Q_SIGNALS:
    void before_destroy();
};
