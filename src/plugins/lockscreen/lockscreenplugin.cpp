// Copyright (C) 2024 <PERSON><PERSON><PERSON> <<EMAIL>>.
// SPDX-License-Identifier: Apache-2.0 OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only

#include "lockscreenplugin.h"

#include "core/qmlengine.h"
#include "interfaces/proxyinterface.h"
#include "output/output.h"
#include "seat/helper.h"

void LockScreenPlugin::initialize(TreelandProxyInterface *proxy)
{
    m_proxy = proxy;

    new (&m_lockscreenComponent) QQmlComponent(m_proxy->qmlEngine(), "LockScreen", "Greeter", this);
}

void LockScreenPlugin::shutdown()
{
    m_proxy = nullptr;
}

QQuickItem *LockScreenPlugin::createLockScreen(Output *output, QQuickItem *parent)
{
    return m_proxy->qmlEngine()->createComponent(
        m_lockscreenComponent,
        parent,
        { { "output", QVariant::fromValue(output->output()) },
          { "outputItem", QVariant::fromValue(output->outputItem()) } });
}
