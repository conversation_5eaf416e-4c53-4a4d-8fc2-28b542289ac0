// Copyright (C) 2023 Ji<PERSON><PERSON> Zhang <<EMAIL>>.
// SPDX-License-Identifier: Apache-2.0 OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only

#include "helper.h"

#include "modules/capture/capture.h"
#include "utils/cmdline.h"
#include "modules/dde-shell/ddeshellattached.h"
#include "modules/dde-shell/ddeshellmanagerinterfacev1.h"
#include "input/inputdevice.h"
#include "core/layersurfacecontainer.h"
#include "greeter/usermodel.h"

#include <rhi/qrhi.h>

#include <QDBusConnection>
#include <QDBusInterface>
#ifndef DISABLE_DDM
#  include "core/lockscreen.h"
#endif
#include "interfaces/multitaskviewinterface.h"
#include "output/output.h"
#include "modules/primary-output/outputmanagement.h"
#include "modules/personalization/personalizationmanager.h"
#include "core/qmlengine.h"
#include "core/rootsurfacecontainer.h"
#include "core/shellhandler.h"
#include "modules/shortcut/shortcutmanager.h"
#include "surface/surfacecontainer.h"
#include "surface/surfacewrapper.h"
#include "input/togglablegesture.h"
#include "config/treelandconfig.h"
#include "modules/wallpaper-color/wallpapercolor.h"
#include "core/windowpicker.h"
#include "workspace/workspace.h"

#include <xcb/xcb.h>
#include <xcb/xproto.h>

#include <WBackend>
#include <WForeignToplevel>
#include <WOutput>
#include <WServer>
#include <WSurfaceItem>
#include <WXdgOutput>
#include <wcursorshapemanagerv1.h>
#include <wlayersurface.h>
#include <woutputitem.h>
#include <woutputlayout.h>
#include <woutputmanagerv1.h>
#include <woutputrenderwindow.h>
#include <woutputviewport.h>
#include <wqmlcreator.h>
#include <wquickcursor.h>
#include <wrenderhelper.h>
#include <wseat.h>
#include <wsocket.h>
#include <wtoplevelsurface.h>
#include <wxdgshell.h>
#include <wxwayland.h>
#include <wxwaylandsurface.h>
#include <wxdgtoplevelsurface.h>

#include <algorithm> // 添加标准库算法支持，用于std::count_if

#include <qwallocator.h>
#include <qwbackend.h>
#include <qwbuffer.h>
#include <qwcompositor.h>
#include <qwdatacontrolv1.h>
#include <qwdatadevice.h>
#include <qwdisplay.h>
#include <qwfractionalscalemanagerv1.h>
#include <qwgammacontorlv1.h>
#include <qwlayershellv1.h>
#include <qwlogging.h>
#include <qwoutput.h>
#include <qwrenderer.h>
#include <qwscreencopyv1.h>
#include <qwsubcompositor.h>
#include <qwviewporter.h>
#include <qwxwaylandsurface.h>
#include <qwoutputpowermanagementv1.h>
#include <qwidlenotifyv1.h>
#include <qwidleinhibitv1.h>

#include <QAction>
#include <QKeySequence>
#include <QLoggingCategory>
#include <QMouseEvent>
#include <QQmlContext>
#include <QQuickWindow>
#include <QtConcurrent>

#include <pwd.h>
#include <utility>

#define WLR_FRACTIONAL_SCALE_V1_VERSION 1
#define _DEEPIN_NO_TITLEBAR "_DEEPIN_NO_TITLEBAR"

static xcb_atom_t internAtom(xcb_connection_t *connection, const char *name, bool onlyIfExists)
{
    if (!name || *name == 0)
        return XCB_NONE;

    xcb_intern_atom_cookie_t cookie = xcb_intern_atom(connection, onlyIfExists, strlen(name), name);
    xcb_intern_atom_reply_t *reply = xcb_intern_atom_reply(connection, cookie, 0);

    if (!reply)
        return XCB_NONE;

    xcb_atom_t atom = reply->atom;
    free(reply);

    return atom;
}

static QByteArray readWindowProperty(xcb_connection_t *connection,
                                     xcb_window_t win,
                                     xcb_atom_t atom,
                                     xcb_atom_t type)
{
    QByteArray data;
    int offset = 0;
    int remaining = 0;

    do {
        xcb_get_property_cookie_t cookie =
            xcb_get_property(connection, false, win, atom, type, offset, 1024);
        xcb_get_property_reply_t *reply = xcb_get_property_reply(connection, cookie, NULL);
        if (!reply)
            break;

        remaining = 0;

        if (reply->type == type) {
            int len = xcb_get_property_value_length(reply);
            char *datas = (char *)xcb_get_property_value(reply);
            data.append(datas, len);
            remaining = reply->bytes_after;
            offset += len;
        }

        free(reply);
    } while (remaining > 0);

    return data;
}

Q_LOGGING_CATEGORY(qLcHelper, "treeland.helper");

Helper *Helper::m_instance = nullptr;

Helper::Helper(QObject *parent)
    : WSeatEventFilter(parent)
    , m_renderWindow(new WOutputRenderWindow(this))
    , m_server(new WServer(this))
    , m_rootSurfaceContainer(new RootSurfaceContainer(m_renderWindow->contentItem()))
    , m_windowGesture(new TogglableGesture(this))
    , m_multiTaskViewGesture(new TogglableGesture(this))
{
    Q_ASSERT(!m_instance);
    m_instance = this;

    m_renderWindow->setColor(Qt::black);
    m_rootSurfaceContainer->setFlag(QQuickItem::ItemIsFocusScope, true);
#if QT_VERSION >= QT_VERSION_CHECK(6, 7, 0)
    m_rootSurfaceContainer->setFocusPolicy(Qt::StrongFocus);
#endif

    m_shellHandler = new ShellHandler(m_rootSurfaceContainer);

    m_workspaceScaleAnimation = new QPropertyAnimation(m_shellHandler->workspace(), "scale", this);
    m_workspaceOpacityAnimation =
        new QPropertyAnimation(m_shellHandler->workspace(), "opacity", this);

    m_workspaceScaleAnimation->setDuration(1000);
    m_workspaceOpacityAnimation->setDuration(1000);
    m_workspaceScaleAnimation->setEasingCurve(QEasingCurve::OutExpo);
    m_workspaceOpacityAnimation->setEasingCurve(QEasingCurve::OutExpo);

        // 重要：对窗口焦点变化的处理是多座位环境下的关键
    // 在这个新版本中，我们完全改变了处理方式，确保座位间的独立性
    connect(m_renderWindow, &QQuickWindow::activeFocusItemChanged, this, [this]() {
        auto wrapper = keyboardFocusSurface();
        if (!wrapper)
            return;
            
        // 找出最近与此表面交互的座位 - 这是触发焦点变化的座位
        WSeat *interactingSeat = getLastInteractingSeat(wrapper);
        
        // 获取当前的物理时间，用于判断这次焦点变化是否是自然发生的还是程序触发的
        qint64 currentTime = QDateTime::currentMSecsSinceEpoch();
        qint64 lastInteractionTime = wrapper->property("lastInteractionTime").toLongLong();
        bool isRecentInteraction = (currentTime - lastInteractionTime) < 500; // 增加阈值到1000ms
        

        
        // 修复：使用更智能的焦点处理逻辑
        if (interactingSeat) {
            // 如果有交互座位，无论时间如何，都为该座位设置焦点
            // 这解决了seat1点击后seat0键盘输入到seat1窗口的问题
            qDebug() << "[FOCUS] Setting keyboard focus for interacting seat" << interactingSeat->name();
            
            // 更新交互记录
            updateSurfaceSeatInteraction(wrapper, interactingSeat);
            
            // 设置键盘焦点
            interactingSeat->setKeyboardFocusSurface(wrapper->surface());
            m_seatKeyboardFocusSurfaces[interactingSeat] = wrapper;
            
            // 记录当前的焦点状态
            qDebug() << "[FOCUS] Current keyboard focus state:";
        for (auto seat : m_seatManager->seats()) {
                auto focusSurface = m_seatKeyboardFocusSurfaces.value(seat);
                qDebug() << "[FOCUS]   - Seat:" << seat->name() 
                         << "focus:" << (focusSurface ? focusSurface->shellSurface()->appId() : "none");
            }
        } else {
            // 如果不清楚是哪个座位触发的，尝试查找哪个座位当前指向此表面
            WSeat *pointerFocusSeat = nullptr;
            for (auto seat : m_seatManager->seats()) {
                if (seat->pointerFocusSurface() == wrapper->surface()) {
                    pointerFocusSeat = seat;
                    qDebug() << "[FOCUS] Found seat with pointer focus on this surface:" << seat->name();
                    break;
                }
            }
            
            if (pointerFocusSeat) {
                // 如果找到了指针焦点座位，为其设置键盘焦点
                qDebug() << "[FOCUS] Setting keyboard focus based on pointer focus for seat" << pointerFocusSeat->name();
                pointerFocusSeat->setKeyboardFocusSurface(wrapper->surface());
                m_seatKeyboardFocusSurfaces[pointerFocusSeat] = wrapper;
                updateSurfaceSeatInteraction(wrapper, pointerFocusSeat);
            } else {
                // 如果完全无法确定，则维持当前焦点状态
                qDebug() << "[FOCUS] Cannot determine seat for focus change, maintaining separate seat focuses";
            }
        }
    });

    connect(m_multiTaskViewGesture,
            &TogglableGesture::statusChanged,
            this,
            [this](TogglableGesture::Status status) {
                if (status == TogglableGesture::Inactive || status == TogglableGesture::Stopped) {
                    m_multitaskView->setStatus(IMultitaskView::Exited);
                } else {
                    m_multitaskView->setStatus(IMultitaskView::Active);
                }
                m_multitaskView->toggleMultitaskView(IMultitaskView::ActiveReason::Gesture);
            });

    connect(m_windowGesture, &TogglableGesture::activated, this, [this]() {
        auto surface = Helper::instance()->activatedSurface();
        if (m_currentMode == CurrentMode::Normal && surface && !surface->isMaximized()) {
            surface->requestMaximize();
        }
    });

    connect(m_windowGesture, &TogglableGesture::deactivated, this, [this]() {
        auto surface = Helper::instance()->activatedSurface();
        if (m_currentMode == CurrentMode::Normal && surface && surface->isMaximized()) {
            surface->requestCancelMaximize();
        }
    });

    connect(m_windowGesture, &TogglableGesture::longPressed, this, [this]() {
        auto surface = Helper::instance()->activatedSurface();
        if (m_currentMode == CurrentMode::Normal && surface && surface->isNormal()) {
            surface->requestMove();
        }
    });

    connect(&TreelandConfig::ref(),
            &TreelandConfig::cursorThemeNameChanged,
            this,
            &Helper::cursorThemeChanged);
    connect(&TreelandConfig::ref(),
            &TreelandConfig::cursorSizeChanged,
            this,
            &Helper::cursorSizeChanged);
}

Helper::~Helper()
{
    for (auto s : m_rootSurfaceContainer->surfaces()) {
        if (auto c = s->container())
            c->removeSurface(s);
    }

    delete m_rootSurfaceContainer;
    Q_ASSERT(m_instance == this);
    m_instance = nullptr;
}

Helper *Helper::instance()
{
    return m_instance;
}

bool Helper::isNvidiaCardPresent()
{
    auto rhi = m_renderWindow->rhi();

    if (!rhi)
        return false;

    QString deviceName = rhi->driverInfo().deviceName;
    qCDebug(qLcHelper) << "Graphics Device:" << deviceName;

    return deviceName.contains("NVIDIA", Qt::CaseInsensitive);
}

void Helper::setWorkspaceVisible(bool visible)
{
    for (auto *surface : m_rootSurfaceContainer->surfaces()) {
        if (surface->type() == SurfaceWrapper::Type::Layer) {
            surface->setHideByLockScreen(m_currentMode == CurrentMode::LockScreen);
        }
    }

    if (visible) {
        m_workspaceScaleAnimation->stop();
        m_workspaceScaleAnimation->setStartValue(m_shellHandler->workspace()->scale());
        m_workspaceScaleAnimation->setEndValue(1.0);
        m_workspaceScaleAnimation->start();

        m_workspaceOpacityAnimation->stop();
        m_workspaceOpacityAnimation->setStartValue(m_shellHandler->workspace()->opacity());
        m_workspaceOpacityAnimation->setEndValue(1.0);
        m_workspaceOpacityAnimation->start();
    } else {
        m_workspaceScaleAnimation->stop();
        m_workspaceScaleAnimation->setStartValue(m_shellHandler->workspace()->scale());
        m_workspaceScaleAnimation->setEndValue(1.4);
        m_workspaceScaleAnimation->start();

        m_workspaceOpacityAnimation->stop();
        m_workspaceOpacityAnimation->setStartValue(m_shellHandler->workspace()->opacity());
        m_workspaceOpacityAnimation->setEndValue(0.0);
        m_workspaceOpacityAnimation->start();
    }
}

QmlEngine *Helper::qmlEngine() const
{
    return qobject_cast<QmlEngine *>(::qmlEngine(this));
}

WOutputRenderWindow *Helper::window() const
{
    return m_renderWindow;
}

ShellHandler *Helper::shellHandler() const
{
    return m_shellHandler;
}

Workspace *Helper::workspace() const
{
    return m_shellHandler->workspace();
}

void Helper::onOutputAdded(WOutput *output)
{
    // TODO: 应该让helper发出Output的信号，每个需要output的单元单独connect。
    allowNonDrmOutputAutoChangeMode(output);
    Output *o;
    if (m_mode == OutputMode::Extension || !m_rootSurfaceContainer->primaryOutput()) {
        o = createNormalOutput(output);
    } else if (m_mode == OutputMode::Copy) {
        o = createCopyOutput(output, m_rootSurfaceContainer->primaryOutput());
    }
    m_outputList.append(o);
    o->enable();
    m_outputManager->newOutput(output);

    m_wallpaperColorV1->updateWallpaperColor(output->name(),
                                             m_personalization->backgroundIsDark(output->name()));

    // 将输出设备分配给适当的座位
    // assignOutputToSeat(output);

    QString cache_location = QStandardPaths::writableLocation(QStandardPaths::AppConfigLocation);
    QSettings settings(cache_location + "/output.ini", QSettings::IniFormat);
    settings.beginGroup(QString("output.%1").arg(output->name()));
    if (settings.contains("scale") && m_mode != OutputMode::Copy) {
        qw_output_state newState;
        newState.set_enabled(true);

        int width = settings.value("width").toInt();
        int height = settings.value("height").toInt();
        int refresh = settings.value("refresh").toInt();

        wlr_output_mode *mode, *configMode = nullptr;
        wl_list_for_each(mode, &output->nativeHandle()->modes, link) {
            if (mode->width == width && mode->height == height && mode->refresh == refresh) {
                configMode = mode;
                break;
            }
        }
        if (configMode)
            newState.set_mode(configMode);
        else
            newState.set_custom_mode(width,
                                     height,
                                     refresh);

        newState.set_adaptive_sync_enabled(settings.value("adaptiveSyncEnabled").toBool());
        newState.set_transform(static_cast<wl_output_transform>(settings.value("transform").toInt()));
        newState.set_scale(settings.value("scale").toFloat());
        output->handle()->commit_state(newState);
    }
    settings.endGroup();
}

void Helper::onOutputRemoved(WOutput *output)
{
    auto index = indexOfOutput(output);
    Q_ASSERT(index >= 0);
    const auto o = m_outputList.takeAt(index);

    const auto &surfaces = getWorkspaceSurfaces(o);
    if (m_mode == OutputMode::Extension) {
        m_rootSurfaceContainer->removeOutput(o);
    } else if (m_mode == OutputMode::Copy) {
        m_mode = OutputMode::Extension;
        if (output == m_rootSurfaceContainer->primaryOutput()->output())
            m_rootSurfaceContainer->removeOutput(o);

        for (int i = 0; i < m_outputList.size(); i++) {
            if (m_outputList.at(i) == m_rootSurfaceContainer->primaryOutput())
                continue;
            Output *o1 = createNormalOutput(m_outputList.at(i)->output());
            o1->enable();
            m_outputList.at(i)->deleteLater();
            m_outputList.replace(i, o1);
        }
    }

    // When removing the last screen, no need to move the window position
    if (m_rootSurfaceContainer->primaryOutput() != o) {
        moveSurfacesToOutput(surfaces, m_rootSurfaceContainer->primaryOutput(), o);
    }

    m_outputManager->removeOutput(output);
    delete o;
}

void Helper::onSurfaceModeChanged(WSurface *surface, WXdgDecorationManager::DecorationMode mode)
{
    auto s = m_rootSurfaceContainer->getSurface(surface);
    if (!s)
        return;
    s->setNoDecoration(mode != WXdgDecorationManager::Server);
}

void Helper::setGamma(struct wlr_gamma_control_manager_v1_set_gamma_event *event)
{
    auto *qwOutput = qw_output::from(event->output);
    size_t ramp_size = 0;
    uint16_t *r = nullptr, *g = nullptr, *b = nullptr;
    wlr_gamma_control_v1 *gamma_control = event->control;
    if (gamma_control) {
        ramp_size = gamma_control->ramp_size;
        r = gamma_control->table;
        g = gamma_control->table + gamma_control->ramp_size;
        b = gamma_control->table + 2 * gamma_control->ramp_size;
    }
    qw_output_state newState;
    newState.set_gamma_lut(ramp_size, r, g, b);
    if (!qwOutput->commit_state(newState)) {
        qCWarning(qLcHelper) << "Failed to set gamma lut!";
        // TODO: use software impl it.
        qw_gamma_control_v1::from(gamma_control)->send_failed_and_destroy();
    }
}

void Helper::onOutputTestOrApply(qw_output_configuration_v1 *config, bool onlyTest)
{
    QList<WOutputState> states = m_outputManager->stateListPending();
    bool ok = true;
    for (auto state : std::as_const(states)) {
        WOutput *output = state.output;
        qw_output_state newState;
        newState.set_enabled(state.enabled);
        if (state.enabled) {
            if (state.mode)
                newState.set_mode(state.mode);
            else
                newState.set_custom_mode(state.customModeSize.width(),
                                         state.customModeSize.height(),
                                         state.customModeRefresh);

            newState.set_adaptive_sync_enabled(state.adaptiveSyncEnabled);
            if (!onlyTest) {
                newState.set_transform(static_cast<wl_output_transform>(state.transform));
                newState.set_scale(state.scale);

                WOutputViewport *viewport = getOutput(output)->screenViewport();
                if (viewport) {
                    auto outputItem = qobject_cast<WOutputItem*>(viewport->parentItem());
                    if (outputItem) {
                        outputItem->setX(state.x);
                        outputItem->setY(state.y);
                    }
                }
            }
        }

        if (onlyTest)
            ok &= output->handle()->test_state(newState);
        else
            ok &= output->handle()->commit_state(newState);
    }
    if (ok && !onlyTest) {
        QString cache_location = QStandardPaths::writableLocation(QStandardPaths::AppConfigLocation);
        QSettings settings(cache_location + "/output.ini", QSettings::IniFormat);
        for (WOutputState state : std::as_const(states)) {
            settings.beginGroup(QString("output.%1").arg(state.output->name()));
            settings.setValue("width", state.mode ? state.mode->width : state.customModeSize.width());
            settings.setValue("height", state.mode ? state.mode->height : state.customModeSize.height());
            settings.setValue("refresh", state.mode ? state.mode->refresh : state.customModeRefresh);
            settings.setValue("transform", state.transform);
            settings.setValue("scale", state.scale);
            settings.setValue("adaptiveSyncEnabled", state.adaptiveSyncEnabled);
            settings.endGroup();
        }
    }
    m_outputManager->sendResult(config, ok);
}

void Helper::onSetOutputPowerMode(wlr_output_power_v1_set_mode_event *event)
{
    auto output = qw_output::from(event->output);
    qw_output_state newState;

    switch (event->mode) {
    case ZWLR_OUTPUT_POWER_V1_MODE_OFF:
        if (!output->handle()->enabled) {
            return;
        }
        newState.set_enabled(false);
        output->commit_state(newState);
        break;
    case ZWLR_OUTPUT_POWER_V1_MODE_ON:
        if (output->handle()->enabled) {
            return;
        }
        newState.set_enabled(true);
        output->commit_state(newState);
        break;
    }
}

void Helper::onNewIdleInhibitor(wlr_idle_inhibitor_v1 *wlr_inhibitor)
{
    auto inhibitor = qw_idle_inhibitor_v1::from(wlr_inhibitor);
    m_idleInhibitors.append(inhibitor);

    connect(inhibitor, &qw_idle_inhibitor_v1::before_destroy, this, [this, inhibitor]() {
        m_idleInhibitors.removeOne(inhibitor);
        updateIdleInhibitor();
    });

    auto wsurface = WSurface::fromHandle(wlr_inhibitor->surface);
    connect(wsurface, &WSurface::mappedChanged, inhibitor, [this]() {
        updateIdleInhibitor();
    });

    auto toplevel = WXdgToplevelSurface::fromSurface(wsurface);
    if (toplevel) {
        connect(toplevel, &WXdgToplevelSurface::minimizeChanged, inhibitor, [this]() {
            updateIdleInhibitor();
        });
    }

    updateIdleInhibitor();
}

void Helper::updateIdleInhibitor()
{
    for (const auto &inhibitor : std::as_const(m_idleInhibitors)) {
        auto wsurface = WSurface::fromHandle((*inhibitor)->surface);
        bool visible = wsurface->mapped();
        auto toplevel = WXdgToplevelSurface::fromSurface(wsurface);
        if (toplevel)
            visible &= !toplevel->isMinimized();

        if (visible) {
            m_idleNotifier->set_inhibited(true);
            return;
        }
    }
    m_idleNotifier->set_inhibited(false);
}

void Helper::onDockPreview(std::vector<SurfaceWrapper *> surfaces,
                           WSurface *target,
                           QPoint pos,
                           ForeignToplevelV1::PreviewDirection direction)
{
    SurfaceWrapper *dockWrapper = m_rootSurfaceContainer->getSurface(target);
    Q_ASSERT(dockWrapper);

    QMetaObject::invokeMethod(m_dockPreview,
                              "show",
                              QVariant::fromValue(surfaces),
                              QVariant::fromValue(dockWrapper),
                              QVariant::fromValue(pos),
                              QVariant::fromValue(direction));
}

void Helper::onDockPreviewTooltip(QString tooltip,
                                  WSurface *target,
                                  QPoint pos,
                                  ForeignToplevelV1::PreviewDirection direction)
{
    SurfaceWrapper *dockWrapper = m_rootSurfaceContainer->getSurface(target);
    Q_ASSERT(dockWrapper);
    QMetaObject::invokeMethod(m_dockPreview,
                              "showTooltip",
                              QVariant::fromValue(tooltip),
                              QVariant::fromValue(dockWrapper),
                              QVariant::fromValue(pos),
                              QVariant::fromValue(direction));
}

void Helper::onShowDesktop()
{
    WindowManagementV1::DesktopState s = m_windowManagement->desktopState();
    if (m_showDesktop == s
        || (s != WindowManagementV1::DesktopState::Normal
            && s != WindowManagementV1::DesktopState::Show))
        return;

    m_showDesktop = s;
    const auto &surfaces = getWorkspaceSurfaces();
    for (auto &surface : surfaces) {
        if (surface->isMinimized()) {
            continue;
        }
        if (s == WindowManagementV1::DesktopState::Normal) {
            surface->startShowDesktopAnimation(true);
        } else if (s == WindowManagementV1::DesktopState::Show) {
            surface->startShowDesktopAnimation(false);
        }
    }
}

void Helper::onSetCopyOutput(treeland_virtual_output_v1 *virtual_output)
{
    Output *mirrorOutput = nullptr;
    for (Output *output : m_outputList) {
        if (!virtual_output->outputList.contains(output->output()->name())) {
            QString screen = output->output()->name() + " does not exist!";
            virtual_output->send_error(TREELAND_VIRTUAL_OUTPUT_V1_ERROR_INVALID_OUTPUT,
                                       screen.toLocal8Bit().data());
            return;
        }

        if (!output->isPrimary()) {
            QString screen =
                output->output()->name() + " is already a copy screen, invalid setting!";
            virtual_output->send_error(TREELAND_VIRTUAL_OUTPUT_V1_ERROR_INVALID_OUTPUT,
                                       screen.toLocal8Bit().data());
            return;
        }

        if (output->output()->name() == virtual_output->outputList.at(0))
            mirrorOutput = output;
    }

    for (int i = 0; i < m_outputList.size(); i++) {
        Output *currentOutput = m_outputList.at(i);
        if (currentOutput == mirrorOutput)
            continue;

        // When setting the primaryOutput as a copy screen, set the mirrorOutput
        // as the home screen.
        if (m_rootSurfaceContainer->primaryOutput() == currentOutput)
            m_rootSurfaceContainer->setPrimaryOutput(mirrorOutput);

        Output *o = createCopyOutput(currentOutput->output(), mirrorOutput);
        m_rootSurfaceContainer->removeOutput(currentOutput);
        currentOutput->deleteLater();
        m_outputList.replace(i, o);
    }

    m_mode = OutputMode::Copy;
    const auto &surfaces = getWorkspaceSurfaces();
    moveSurfacesToOutput(surfaces, mirrorOutput, nullptr);
}

void Helper::onRestoreCopyOutput(treeland_virtual_output_v1 *virtual_output)
{
    for (int i = 0; i < m_outputList.size(); i++) {
        Output *currentOutput = m_outputList.at(i);
        if (currentOutput->output()->name() == virtual_output->outputList.at(0))
            continue;

        Output *o = createNormalOutput(m_outputList.at(i)->output());
        o->enable();
        m_outputList.at(i)->deleteLater();
        m_outputList.replace(i, o);
    }
    m_mode = OutputMode::Extension;
}

void Helper::onSurfaceWrapperAdded(SurfaceWrapper *wrapper)
{
    bool isXdgToplevel = wrapper->type() == SurfaceWrapper::Type::XdgToplevel;
    bool isXdgPopup = wrapper->type() == SurfaceWrapper::Type::XdgPopup;
    bool isXwayland = wrapper->type() == SurfaceWrapper::Type::XWayland;
    bool isLayer = wrapper->type() == SurfaceWrapper::Type::Layer;

    if (isXdgToplevel || isXdgPopup || isLayer) {
        auto *attached =
            new Personalization(wrapper->shellSurface(), m_personalization, wrapper);
        connect(wrapper, &SurfaceWrapper::aboutToBeInvalidated,
                attached, &Personalization::deleteLater);

        auto updateNoTitlebar = [this, attached] {
            auto wrapper = attached->surfaceWrapper();
            if (attached->noTitlebar()) {
                wrapper->setNoTitleBar(true);
                auto layer = qobject_cast<WLayerSurface *>(wrapper->shellSurface());
                if (!isLaunchpad(layer)) {
                    wrapper->setNoDecoration(false);
                }
                return;
            }

            wrapper->resetNoTitleBar();
            wrapper->setNoDecoration(m_xdgDecorationManager->modeBySurface(wrapper->surface())
                                     != WXdgDecorationManager::Server);
        };

        if (isXdgToplevel) {
            connect(
                m_xdgDecorationManager,
                &WXdgDecorationManager::surfaceModeChanged,
                attached,
                [attached, updateNoTitlebar](
                    WAYLIB_SERVER_NAMESPACE::WSurface *surface,
                    [[maybe_unused]] Waylib::Server::WXdgDecorationManager::DecorationMode mode) {
                    if (surface == attached->surfaceWrapper()->surface()) {
                        updateNoTitlebar();
                    }
                });
        }

        connect(attached, &Personalization::windowStateChanged, this, updateNoTitlebar);
        updateNoTitlebar();

        auto updateBlur = [attached] {
            attached->surfaceWrapper()->setBlur(attached->backgroundType() == Personalization::BackgroundType::Blur);
        };
        connect(attached, &Personalization::backgroundTypeChanged, this, updateBlur);
        updateBlur();
        if (isLayer) {
            auto layer = qobject_cast<WLayerSurface *>(wrapper->shellSurface());
            if (isLaunchpad(layer))
                wrapper->setCoverEnabled(true);
        }
    }

    if (isXwayland) {
        auto xwayland = qobject_cast<WXWaylandSurface *>(wrapper->shellSurface());
        auto updateDecorationTitleBar = [this, wrapper, xwayland]() {
            if (!xwayland->isBypassManager()) {
                if (m_atomDeepinNoTitlebar
                    && !readWindowProperty(defaultXWaylandSocket()->xcbConnection(),
                                           xwayland->handle()->handle()->window_id,
                                           m_atomDeepinNoTitlebar,
                                           XCB_ATOM_CARDINAL)
                            .isEmpty()) {
                    wrapper->setNoTitleBar(true);
                } else {
                    wrapper->setNoTitleBar(xwayland->decorationsFlags()
                                           & WXWaylandSurface::DecorationsNoTitle);
                }
                wrapper->setNoDecoration(xwayland->decorationsFlags()
                                         & WXWaylandSurface::DecorationsNoBorder);
            } else {
                wrapper->setNoTitleBar(true);
                wrapper->setNoDecoration(true);
            }
        };
        // When x11 surface dissociate, SurfaceWrapper will be destroyed immediately
        // but WXWaylandSurface will not, so must connect to `wrapper`
        xwayland->safeConnect(&WXWaylandSurface::bypassManagerChanged,
                              wrapper,
                              updateDecorationTitleBar);
        xwayland->safeConnect(&WXWaylandSurface::decorationsFlagsChanged,
                              wrapper,
                              updateDecorationTitleBar);
        updateDecorationTitleBar();
    }

    if (!isLayer) {
        auto windowOverlapChecker = new WindowOverlapChecker(wrapper, wrapper);
    }

#ifndef DISABLE_DDM
    if (isLayer) {
        connect(this, &Helper::currentModeChanged, wrapper, [this, wrapper] {
            wrapper->setHideByLockScreen(m_currentMode == CurrentMode::LockScreen);
        });
        wrapper->setHideByLockScreen(m_currentMode == CurrentMode::LockScreen);
    }
#endif

    if (!wrapper->skipDockPreView()) {
        m_foreignToplevel->addSurface(wrapper->shellSurface());
        m_treelandForeignToplevel->addSurface(wrapper);
    }
    connect(wrapper, &SurfaceWrapper::skipDockPreViewChanged, this, [this, wrapper] {
        if (wrapper->skipDockPreView()) {
            m_foreignToplevel->removeSurface(wrapper->shellSurface());
            m_treelandForeignToplevel->removeSurface(wrapper);
        } else {
            m_foreignToplevel->addSurface(wrapper->shellSurface());
            m_treelandForeignToplevel->addSurface(wrapper);
        }
    });

    // 原有的其他连接逻辑
    connect(wrapper,
            &SurfaceWrapper::destroyed,
            this,
            [this](QObject *obj) {
                auto wrapper = static_cast<SurfaceWrapper *>(obj);
                
                // 清理座位特定的状态
                for (auto seat : m_seatManager->seats()) {
                    if (m_seatActivatedSurfaces.value(seat) == wrapper) {
                        m_seatActivatedSurfaces.remove(seat);
                    }
                    if (m_seatMoveResizeSurfaces.value(seat) == wrapper) {
                        endMoveResizeForSeat(seat);
                    }
                }
                
                if (m_activatedSurface == wrapper) {
                    m_activatedSurface = nullptr;
                    Q_EMIT activatedSurfaceChanged();
                }
            });

    // 添加正确的连接
    if (isXdgToplevel || isXwayland) {
        connect(wrapper, &SurfaceWrapper::requestMinimize, this, [this, wrapper]() {
            if (TreelandConfig::ref().blockActivateSurface())
                return;

            if (m_currentMode == CurrentMode::Normal) {
                if (wrapper->surfaceState() == SurfaceWrapper::State::Minimized)
                    return;

                auto container = wrapper->container();
                if (container) {
                    container->removeSurface(wrapper);
                }
            }
        });
    }

    if (isXdgToplevel || isXwayland) {
        // 新增：拦截移动和调整大小请求，使其支持多座位
        connect(wrapper, &SurfaceWrapper::requestMove, this, [this, wrapper]() {
            qDebug() << "=== MOVE REQUEST DEBUG ===";
            qDebug() << "Surface requested move:" << wrapper;
            
            // 首先尝试从表面属性获取最后交互的座位
            WSeat *requestingSeat = getLastInteractingSeat(wrapper);
            qDebug() << "Last interacting seat:" << (requestingSeat ? requestingSeat->name() : "null");
            
            // 如果没有记录，遍历所有座位查找激活状态
            if (!requestingSeat) {
                qDebug() << "No last interacting seat, checking all seats...";
                for (auto seat : m_seatManager->seats()) {
                    auto activatedSurface = getActivatedSurfaceForSeat(seat);
                    qDebug() << "Seat" << seat->name() << "activated surface:" << activatedSurface;
                    if (activatedSurface == wrapper) {
                        requestingSeat = seat;
                        qDebug() << "Found requesting seat from activated surface:" << seat->name();
                        break;
                    }
                }
            }
            
            // 如果还是找不到，检查哪个座位的指针当前在焦点上
            if (!requestingSeat) {
                qDebug() << "Still no seat found, checking pointer focus...";
                for (auto seat : m_seatManager->seats()) {
                    auto focusSurface = seat->pointerFocusSurface();
                    qDebug() << "Seat" << seat->name() << "pointer focus surface:" << focusSurface;
                    qDebug() << "Seat" << seat->name() << "has cursor:" << (seat->cursor() != nullptr);
                    if (seat->cursor()) {
                        qDebug() << "Seat" << seat->name() << "cursor position:" << seat->cursor()->position();
                        qDebug() << "Seat" << seat->name() << "cursor event window:" << seat->cursor()->eventWindow();
                    }
                    qDebug() << "Seat" << seat->name() << "device count:" << seat->deviceList().size();
                    
                    if (focusSurface && focusSurface->handle()->handle() == wrapper->surface()->handle()->handle()) {
                        requestingSeat = seat;
                        qDebug() << "Found requesting seat from pointer focus:" << seat->name();
                        break;
                    }
                }
            }
            
            // 最后的fallback：使用第一个可用的座位，而不是总是主座位
            if (!requestingSeat) {
                auto seats = m_seatManager->seats();
                if (!seats.isEmpty()) {
                    requestingSeat = seats.first();
                    qDebug() << "Using first available seat as fallback:" << requestingSeat->name();
                } else {
                    qDebug() << "No seats available!";
                    return;
                }
            }
            
            qDebug() << "Final requesting seat:" << requestingSeat->name();
            
            // **重要：强制更新座位交互记录，确保下次能正确识别**
            updateSurfaceSeatInteraction(wrapper, requestingSeat);
            qDebug() << "Force updated seat interaction for future reference";
            
            // 开始座位特定的移动
            beginMoveResizeForSeat(requestingSeat, wrapper, Qt::Edges{});
        }, Qt::DirectConnection);
        
        connect(wrapper, &SurfaceWrapper::requestResize, this, [this, wrapper](Qt::Edges edges) {
            qDebug() << "=== RESIZE REQUEST DEBUG ===";
            qDebug() << "Surface requested resize:" << wrapper << "edges:" << edges;
            
            // 使用与move相同的改进逻辑
            WSeat *requestingSeat = getLastInteractingSeat(wrapper);
            qDebug() << "Last interacting seat:" << (requestingSeat ? requestingSeat->name() : "null");
            
            if (!requestingSeat) {
                qDebug() << "No last interacting seat, checking all seats...";
                for (auto seat : m_seatManager->seats()) {
                    auto activatedSurface = getActivatedSurfaceForSeat(seat);
                    qDebug() << "Seat" << seat->name() << "activated surface:" << activatedSurface;
                    if (activatedSurface == wrapper) {
                        requestingSeat = seat;
                        qDebug() << "Found requesting seat from activated surface:" << seat->name();
                        break;
                    }
                }
            }
            
            if (!requestingSeat) {
                qDebug() << "Still no seat found, checking pointer focus...";
                for (auto seat : m_seatManager->seats()) {
                    auto focusSurface = seat->pointerFocusSurface();
                    qDebug() << "Seat" << seat->name() << "pointer focus surface:" << focusSurface;
                    qDebug() << "Seat" << seat->name() << "has cursor:" << (seat->cursor() != nullptr);
                    if (seat->cursor()) {
                        qDebug() << "Seat" << seat->name() << "cursor position:" << seat->cursor()->position();
                        qDebug() << "Seat" << seat->name() << "cursor event window:" << seat->cursor()->eventWindow();
                    }
                    qDebug() << "Seat" << seat->name() << "device count:" << seat->deviceList().size();
                    
                    if (focusSurface && focusSurface->handle()->handle() == wrapper->surface()->handle()->handle()) {
                        requestingSeat = seat;
                        qDebug() << "Found requesting seat from pointer focus:" << seat->name();
                        break;
                    }
                }
            }
            
            if (!requestingSeat) {
                auto seats = m_seatManager->seats();
                if (!seats.isEmpty()) {
                    requestingSeat = seats.first();
                    qDebug() << "Using first available seat as fallback:" << requestingSeat->name();
                } else {
                    qDebug() << "No seats available!";
                    return;
                }
            }
            
            qDebug() << "Final requesting seat:" << requestingSeat->name();
            
            // **重要：强制更新座位交互记录，确保下次能正确识别**
            updateSurfaceSeatInteraction(wrapper, requestingSeat);
            qDebug() << "Force updated seat interaction for future reference";
            
            // 开始座位特定的调整大小
            beginMoveResizeForSeat(requestingSeat, wrapper, edges);
        }, Qt::DirectConnection);
    }
}

void Helper::onSurfaceWrapperAboutToRemove(SurfaceWrapper *wrapper)
{
    if (!wrapper->skipDockPreView()) {
        m_foreignToplevel->removeSurface(wrapper->shellSurface());
        m_treelandForeignToplevel->removeSurface(wrapper);
    }
}

bool Helper::surfaceBelongsToCurrentUser(SurfaceWrapper *wrapper)
{
    static const int puid = getuid();
    auto credentials = WClient::getCredentials(wrapper->surface()->waylandClient()->handle());
    auto user = m_userModel->currentUser();
    if (user) {
        // FIXME: XWayland's surfaces' uid are all puid now, will change to per user
        // XWayland instance in the future
        return credentials->uid == user->UID() || credentials->uid == puid;
    } else {
        return credentials->uid == puid;
    }
}

void Helper::deleteTaskSwitch()
{
    if (m_taskSwitch) {
        m_taskSwitch->deleteLater();
        m_taskSwitch = nullptr;
    }
}

void Helper::init()
{
    auto engine = qmlEngine();
    m_userModel = engine->singletonInstance<UserModel *>("Treeland", "UserModel");

    engine->setContextForObject(m_renderWindow, engine->rootContext());
    engine->setContextForObject(m_renderWindow->contentItem(), engine->rootContext());
    m_rootSurfaceContainer->setQmlEngine(engine);
    m_rootSurfaceContainer->init(m_server);

    // 先初始化后端
    m_backend = m_server->attach<WBackend>();
    qDebug() << "Backend initialized";
    
    // 初始化座位管理器
    m_seatManager = m_server->attach<WSeatManager>();
    qDebug() << "Seat manager initialized";

    m_outputManager = m_server->attach<WOutputManagerV1>();
    connect(m_backend, &WBackend::outputAdded, this, &Helper::onOutputAdded);
    connect(m_backend, &WBackend::outputRemoved, this, &Helper::onOutputRemoved);
    
    connect(m_backend, &WBackend::inputRemoved, this, [this](WInputDevice *device) {
        qDebug() << "Input device removed:" << device->name();
        // 从所有座位中移除设备
        for (auto seat : m_seatManager->seats()) {
            if (seat->deviceList().contains(device)) {
                qDebug() << "Removing device from seat:" << seat->name();
                seat->detachInputDevice(device);
            }
        }
    });

    m_ddeShellV1 = m_server->attach<DDEShellManagerInterfaceV1>();
    connect(m_ddeShellV1, &DDEShellManagerInterfaceV1::toggleMultitaskview, this, [this] {
        if (m_multitaskView) {
            m_multitaskView->toggleMultitaskView(IMultitaskView::ActiveReason::ShortcutKey);
        }
    });
    connect(m_ddeShellV1,
            &DDEShellManagerInterfaceV1::requestPickWindow,
            this,
            &Helper::handleWindowPicker);
    connect(m_ddeShellV1,
            &DDEShellManagerInterfaceV1::lockScreenCreated,
            this,
            &Helper::handleLockScreen);
    m_shellHandler->createComponent(engine);
    m_shellHandler->initXdgShell(m_server);
    m_shellHandler->initLayerShell(m_server);
    
    // 启动服务器，这会创建原生句柄
    m_server->start();
    qDebug() << "Server started";
    
    // 现在初始化多座位支持 - 确保在服务器启动后初始化
    qDebug() << "Initializing multi-seat support...";
    initMultiSeat();
    qDebug() << "Multi-seat support initialized";
    
    // 初始化输入法，需要在座位初始化后
    m_shellHandler->initInputMethodHelper(m_server, m_seat);

    m_foreignToplevel = m_server->attach<WForeignToplevel>();
    m_treelandForeignToplevel = m_server->attach<ForeignToplevelV1>();
    Q_ASSERT(m_treelandForeignToplevel);
    qmlRegisterSingletonInstance<ForeignToplevelV1>("Treeland.Protocols",
                                                    1,
                                                    0,
                                                    "ForeignToplevelV1",
                                                    m_treelandForeignToplevel);
    qRegisterMetaType<ForeignToplevelV1::PreviewDirection>();

    connect(m_shellHandler,
            &ShellHandler::surfaceWrapperAdded,
            this,
            &Helper::onSurfaceWrapperAdded);

    connect(m_shellHandler,
            &ShellHandler::surfaceWrapperAboutToRemove,
            this,
            &Helper::onSurfaceWrapperAboutToRemove);

    auto *xdgOutputManager =
        m_server->attach<WXdgOutputManager>(m_rootSurfaceContainer->outputLayout());

    m_primaryOutputV1 = m_server->attach<PrimaryOutputV1>();
    m_wallpaperColorV1 = m_server->attach<WallpaperColorV1>();
    m_windowManagement = m_server->attach<WindowManagementV1>();
    m_virtualOutput = m_server->attach<VirtualOutputV1>();
    m_shortcut = m_server->attach<ShortcutV1>();
    auto captureManagerV1 = m_server->attach<CaptureManagerV1>();
    captureManagerV1->setOutputRenderWindow(m_renderWindow);

    connect(
        captureManagerV1,
        &CaptureManagerV1::contextInSelectionChanged,
        this,
        [this, captureManagerV1] {
            if (captureManagerV1->contextInSelection()) {
                m_captureSelector = qobject_cast<CaptureSourceSelector *>(
                    qmlEngine()->createCaptureSelector(m_rootSurfaceContainer, captureManagerV1));
            } else if (m_captureSelector) {
                m_captureSelector->deleteLater();
            }
        });
    m_personalization = m_server->attach<PersonalizationV1>();

    auto updateCurrentUser = [this] {
        auto user = m_userModel->currentUser();
        m_personalization->setUserId(user ? user->UID() : getuid());
    };
    connect(m_userModel, &UserModel::currentUserNameChanged, this, updateCurrentUser);

    updateCurrentUser();

    connect(m_personalization,
            &PersonalizationV1::backgroundChanged,
            this,
            [this](const QString &output, bool isdark) {
                m_wallpaperColorV1->updateWallpaperColor(output, isdark);
            });

    for (auto output : m_rootSurfaceContainer->outputs()) {
        const QString &outputName = output->output()->name();
        m_wallpaperColorV1->updateWallpaperColor(outputName,
                                                 m_personalization->backgroundIsDark(outputName));
    }

    connect(m_windowManagement,
            &WindowManagementV1::desktopStateChanged,
            this,
            &Helper::onShowDesktop);

    connect(m_virtualOutput,
            &VirtualOutputV1::requestCreateVirtualOutput,
            this,
            &Helper::onSetCopyOutput);

    connect(m_virtualOutput,
            &VirtualOutputV1::destroyVirtualOutput,
            this,
            &Helper::onRestoreCopyOutput);

    connect(m_primaryOutputV1,
            &PrimaryOutputV1::requestSetPrimaryOutput,
            this,
            [this](const char *name) {
                for (auto &&output : m_rootSurfaceContainer->outputs()) {
                    if (strcmp(output->output()->nativeHandle()->name, name) == 0) {
                        m_rootSurfaceContainer->setPrimaryOutput(output);
                    }
                }
            });

    connect(m_rootSurfaceContainer, &RootSurfaceContainer::primaryOutputChanged, this, [this]() {
        if (m_rootSurfaceContainer->primaryOutput()) {
            m_primaryOutputV1->sendPrimaryOutput(
                m_rootSurfaceContainer->primaryOutput()->output()->nativeHandle()->name);
            if (m_lockScreen) {
                m_lockScreen->setPrimaryOutputName(m_rootSurfaceContainer->primaryOutput()->output()->name());
            }
        }
    });

    qmlRegisterUncreatableType<Personalization>("Treeland.Protocols",
                                                1,
                                                0,
                                                "Personalization",
                                                "Only for Enum");

    qmlRegisterUncreatableType<DDEShellHelper>("Treeland.Protocols",
                                               1,
                                               0,
                                               "DDEShellHelper",
                                               "Only for attached");
    qmlRegisterUncreatableType<CaptureSource>("Treeland.Protocols",
                                              1,
                                              0,
                                              "CaptureSource",
                                              "An abstract class");
    qmlRegisterType<CaptureContextV1>("Treeland.Protocols", 1, 0, "CaptureContextV1");
    qmlRegisterType<CaptureSourceSelector>("Treeland.Protocols", 1, 0, "CaptureSourceSelector");

    m_renderer = WRenderHelper::createRenderer(m_backend->handle());
    if (!m_renderer) {
        qCFatal(qLcHelper) << "Failed to create renderer";
    }

    m_allocator = qw_allocator::autocreate(*m_backend->handle(), *m_renderer);
    m_renderer->init_wl_display(*m_server->handle());

    // free follow display
    m_compositor = qw_compositor::create(*m_server->handle(), 6, *m_renderer);
    qw_subcompositor::create(*m_server->handle());
    qw_screencopy_manager_v1::create(*m_server->handle());
    qw_viewporter::create(*m_server->handle());
    m_renderWindow->init(m_renderer, m_allocator);

    // for xwayland
    auto *xwaylandOutputManager =
        m_server->attach<WXdgOutputManager>(m_rootSurfaceContainer->outputLayout());
    xwaylandOutputManager->setScaleOverride(1.0);
    m_defaultXWayland = m_shellHandler->createXWayland(m_server, m_seat, m_compositor, false);
    connect(m_defaultXWayland, &WXWayland::ready, this, [this] {
        m_atomDeepinNoTitlebar =
            internAtom(m_defaultXWayland->xcbConnection(), _DEEPIN_NO_TITLEBAR, false);
        if (!m_atomDeepinNoTitlebar) {
            qWarning() << "failed internAtom:" << _DEEPIN_NO_TITLEBAR;
        }
    });
    xdgOutputManager->setFilter([this] (WClient *client) {
        return client != m_defaultXWayland->waylandClient();
    });
    xwaylandOutputManager->setFilter([this] (WClient *client) {
        return client == m_defaultXWayland->waylandClient();
    });
    m_xdgDecorationManager = m_server->attach<WXdgDecorationManager>();
    connect(m_xdgDecorationManager,
            &WXdgDecorationManager::surfaceModeChanged,
            this,
            &Helper::onSurfaceModeChanged);

    bool freezeClientWhenDisable = false;
    m_socket = new WSocket(freezeClientWhenDisable);
    if (m_socket->autoCreate()) {
        m_server->addSocket(m_socket);
        Q_EMIT socketFileChanged();
    } else {
        delete m_socket;
        qCCritical(qLcHelper) << "Failed to create socket";
        return;
    }

    auto gammaControlManager = qw_gamma_control_manager_v1::create(*m_server->handle());
    connect(gammaControlManager,
            &qw_gamma_control_manager_v1::notify_set_gamma,
            this,
            &Helper::setGamma);

    connect(m_outputManager,
            &WOutputManagerV1::requestTestOrApply,
            this,
            &Helper::onOutputTestOrApply);

    m_server->attach<WCursorShapeManagerV1>();
    qw_fractional_scale_manager_v1::create(*m_server->handle(), WLR_FRACTIONAL_SCALE_V1_VERSION);
    qw_data_control_manager_v1::create(*m_server->handle());

    m_dockPreview = engine->createDockPreview(m_renderWindow->contentItem());

    connect(m_treelandForeignToplevel,
            &ForeignToplevelV1::requestDockPreview,
            this,
            &Helper::onDockPreview);
    connect(m_treelandForeignToplevel,
            &ForeignToplevelV1::requestDockPreviewTooltip,
            this,
            &Helper::onDockPreviewTooltip);

    connect(m_treelandForeignToplevel,
            &ForeignToplevelV1::requestDockClose,
            m_dockPreview,
            [this]() {
                QMetaObject::invokeMethod(m_dockPreview, "close");
            });


    m_idleNotifier = qw_idle_notifier_v1::create(*m_server->handle());

    m_idleInhibitManager = qw_idle_inhibit_manager_v1::create(*m_server->handle());
    connect(m_idleInhibitManager, &qw_idle_inhibit_manager_v1::notify_new_inhibitor, this, &Helper::onNewIdleInhibitor);

    m_outputPowerManager = qw_output_power_manager_v1::create(*m_server->handle());

    connect(m_outputPowerManager, &qw_output_power_manager_v1::notify_set_mode, this, &Helper::onSetOutputPowerMode);

    m_backend->handle()->start();

    qCInfo(qLcHelper) << "Listing on:" << m_socket->fullServerName();
}

void Helper::initMultiSeat()
{
    // 配置路径
    m_seatConfigPath = QStandardPaths::writableLocation(QStandardPaths::AppConfigLocation) + "/seats.json";

    // 加载座位配置
    loadSeatConfig();
    
    // 初始化座位
    if (m_seatManager->seats().isEmpty()) {
        // 创建默认座位
        WSeat *defaultSeat = m_seatManager->createSeat("seat0", true);
        if (!defaultSeat) {
            qCritical() << "Failed to create default seat!";
            return;
        }
        
        // 确保有原生句柄
        if (!defaultSeat->nativeHandle()) {
            m_server->attach(defaultSeat);
        }
        
        m_seat = defaultSeat;
        
        // 添加默认规则
        m_seatManager->addDeviceRule("seat0", "1:.*"); // 键盘设备
        m_seatManager->addDeviceRule("seat0", "2:.*"); // 指针设备
        m_seatManager->addDeviceRule("seat0", "3:.*"); // 触摸设备
    } else {
        // 使用第一个座位作为主座位
        m_seat = m_seatManager->seats().first();
        
        // 确保有原生句柄
        if (!m_seat->nativeHandle()) {
            m_server->attach(m_seat);
        }
        
        // 确保有基本规则
        if (m_seatManager->deviceRules(m_seat->name()).isEmpty()) {
            m_seatManager->addDeviceRule(m_seat->name(), "1:.*"); // 键盘设备
            m_seatManager->addDeviceRule(m_seat->name(), "2:.*"); // 指针设备
            m_seatManager->addDeviceRule(m_seat->name(), "3:.*"); // 触摸设备
        }
    }
    
    // 确保有有效的座位
    if (!m_seat) {
        m_seat = m_seatManager->createSeat("seat0", true);
        if (!m_seat) {
            qCritical() << "Critical error: Cannot create any seat!";
            return;
        }
        
        if (!m_seat->nativeHandle()) {
            m_server->attach(m_seat);
        }
    }
    
    // 设置事件过滤器
    if (!m_seat->eventFilter()) {
    m_seat->setEventFilter(this);
    }
    
    // 配置座位
    setupSeatsConfiguration();
    connectDeviceSignals();
    assignExistingDevices();
    
    // 定期检查并修复座位设备分配
    QTimer::singleShot(1000, this, &Helper::checkAndFixSeatDevices);
}

void Helper::loadSeatConfig()
{
    QFile file(m_seatConfigPath);
    if (file.open(QIODevice::ReadOnly)) {
        QJsonDocument doc = QJsonDocument::fromJson(file.readAll());
        m_seatManager->loadConfig(doc.object());
        qInfo() << "Loaded seat configuration from" << m_seatConfigPath;
    } else {
        qDebug() << "No seat configuration file found at" << m_seatConfigPath << ", will use defaults";
    }
}

void Helper::saveSeatConfig()
{
    QFile file(m_seatConfigPath);
    if (file.open(QIODevice::WriteOnly)) {
        QJsonDocument doc(m_seatManager->saveConfig());
        file.write(doc.toJson());
    }
}

WSeatManager *Helper::seatManager() const
{
    return m_seatManager;
}

WSeat *Helper::getSeatForOutput(Output *output) const
{
    if (!output)
        return m_seat;
        
    WOutput *woutput = output->output();
    
    // 查找包含此输出设备的座位
    for (auto seat : m_seatManager->seats()) {
        if (seat->outputs().contains(woutput)) {
            return seat;
        }
    }
    
    // 如果没找到，返回默认座位
    return m_seat;
}

WSeat *Helper::getSeatForDevice(WInputDevice *device) const
{
    if (!device) {
        return nullptr;
    }
    
    // 首先检查设备是否已经分配给了某个座位
    for (auto seat : m_seatManager->seats()) {
        if (seat->deviceList().contains(device)) {
            return seat;
        }
    }
    
    // 如果设备没有分配到任何座位，尝试通过规则匹配找到合适的座位
    // 获取设备信息用于匹配
    QString deviceName = device->name();
    WInputDevice::Type deviceType = device->type();
    QString deviceInfo = QString("%1:%2").arg(static_cast<int>(deviceType)).arg(deviceName);
    
    // 遍历所有座位，找到匹配的规则
    for (auto seat : m_seatManager->seats()) {
        QStringList rules = m_seatManager->deviceRules(seat->name());
        for (const QString &ruleStr : rules) {
            QRegularExpression rule(ruleStr);
            if (rule.isValid() && rule.match(deviceInfo).hasMatch()) {
                return seat;
            }
        }
    }
    
    // 如果没有找到匹配的座位，返回fallback座位
    return m_seatManager->fallbackSeat();
}

bool Helper::socketEnabled() const
{
    return m_socket->isEnabled();
}

void Helper::setSocketEnabled(bool newEnabled)
{
    if (m_socket)
        m_socket->setEnabled(newEnabled);
    else
        qCWarning(qLcHelper) << "Can't set enabled for empty socket!";
}

void Helper::activateSurface(SurfaceWrapper *wrapper, Qt::FocusReason reason)
{
    // 找出当前可能的交互座位 - 这可能是null
    WSeat *interactingSeat = getLastInteractingSeat(wrapper);
    
    // 主座位默认为m_seat
    WSeat *primarySeat = m_seat;
    
    qDebug() << "[FOCUS] activateSurface called: surface=" << wrapper
             << "interacting_seat=" << (interactingSeat ? interactingSeat->name() : "none")
             << "reason=" << reason;
    
    if (TreelandConfig::ref().blockActivateSurface() && wrapper) {
        if (wrapper->shellSurface()->hasCapability(WToplevelSurface::Capability::Activate)) {
            workspace()->pushActivedSurface(wrapper);
        }
        return;
    }
    
    if (!wrapper
        || !wrapper->shellSurface()->hasCapability(WToplevelSurface::Capability::Activate)) {
        if (!wrapper)
            setActivatedSurface(nullptr);
        // else if wrapper don't have Activate Capability, do nothing
        // Otherwise, when click the dock, the last activate application will immediately
        // lose focus, and The dock will reactivate it instead of minimizing it
    } else {
        if (wrapper->hasActiveCapability()) {
            setActivatedSurface(wrapper);
        } else {
            qCritical() << "Try activate a surface which don't have ActiveCapability!";
        }
    }

    // 重要修复：根据交互座位决定键盘焦点的设置
    if (!wrapper
        || (wrapper->shellSurface()->hasCapability(WToplevelSurface::Capability::Focus)
            && wrapper->acceptKeyboardFocus())) {
        
        // 确定应该设置哪个座位的焦点
        WSeat *targetSeat = interactingSeat ? interactingSeat : primarySeat;
        
        qDebug() << "[FOCUS] Setting keyboard focus for seat: " << targetSeat->name();
        
        if (targetSeat && targetSeat->nativeHandle()) {
            // 设置UI焦点
            if (wrapper) {
                wrapper->setFocus(true, reason);
            } else if (auto currentFocus = keyboardFocusSurface()) {
                currentFocus->setFocus(false, reason);
            }
            
            // 设置交互座位的键盘焦点
            if (wrapper) {
                // 更新交互记录
                updateSurfaceSeatInteraction(wrapper, targetSeat);
            }
            
            // 直接设置目标座位的键盘焦点
            targetSeat->setKeyboardFocusSurface(wrapper ? wrapper->surface() : nullptr);
            if (wrapper) {
                m_seatKeyboardFocusSurfaces[targetSeat] = wrapper;
            } else {
                m_seatKeyboardFocusSurfaces.remove(targetSeat);
            }
            
            qDebug() << "[FOCUS] Set keyboard focus for seat" 
                     << targetSeat->name() << "to:" 
                     << (wrapper ? wrapper->shellSurface()->appId() : "null");
                     
            // 记录当前的焦点状态
            qDebug() << "[FOCUS] Current keyboard focus state:";
            for (auto seat : m_seatManager->seats()) {
                auto focusSurface = m_seatKeyboardFocusSurfaces.value(seat);
                qDebug() << "[FOCUS]   - Seat:" << seat->name() 
                         << "focus:" << (focusSurface ? focusSurface->shellSurface()->appId() : "none");
            }
        }
    }
}

void Helper::forceActivateSurface(SurfaceWrapper *wrapper, Qt::FocusReason reason)
{
    if (!wrapper) {
        qCCritical(qLcHelper) << "Don't force activate to empty surface! do you want `Helper::activeSurface(nullptr)`?";
        return;
    }

    restoreFromShowDesktop(wrapper);

    if (wrapper->isMinimized()) {
        wrapper->requestCancelMinimize(
            !(reason == Qt::TabFocusReason || reason == Qt::BacktabFocusReason));
    }

    if (!wrapper->surface()->mapped()) {
        qCWarning(qLcHelper) << "Can't activate unmapped surface: " << wrapper;
        return;
    }

    if (!wrapper->showOnWorkspace(workspace()->current()->id()))
        workspace()->switchTo(workspace()->modelIndexOfSurface(wrapper));
    Helper::instance()->activateSurface(wrapper, reason);
}

RootSurfaceContainer *Helper::rootContainer() const
{
    return m_rootSurfaceContainer;
}

void Helper::fakePressSurfaceBottomRightToReszie(SurfaceWrapper *surface)
{
    auto position = surface->geometry().bottomRight();
    m_fakelastPressedPosition = position;
    m_seat->setCursorPosition(position);
    Q_EMIT surface->requestResize(Qt::BottomEdge | Qt::RightEdge);
}

bool Helper::beforeDisposeEvent(WSeat *seat, QWindow *targetWindow, QInputEvent *event)
{
    if (Q_UNLIKELY(!targetWindow)) {
        qWarning() << "beforeDisposeEvent called with null window";
        return false;
    }

    if (event->isInputEvent()) {
        m_idleNotifier->notify_activity(seat->nativeHandle());
    }

    // 获取事件关联的seat - 在这里获取一次，后面都复用此变量
    WSeat *eventSeat = getSeatForEvent(event);

    // 确定事件应该路由到哪个座位
    WSeat *targetSeat = seat;
    
    if (event->device()) {
        WInputDevice *device = WInputDevice::from(event->device());
        if (device) {
            // 找到设备所属的座位
            targetSeat = getSeatForDevice(device);
            if (!targetSeat) {
                qWarning() << "Device has no associated seat, using default seat";
                targetSeat = seat;
            }
        }
    }
    
    // 如果事件应该路由到不同的座位，则不处理这个事件
    if (targetSeat && targetSeat != seat) {
        // 我们不能直接调用targetSeat的protected方法
        // 但这个事件应该会在后续被正确地分发到targetSeat
        return false;
    }

    // 只有主座位处理全局快捷键和系统功能
    if (seat == m_seat) {
        // 处理键盘事件和快捷键
        if (event->type() == QEvent::KeyPress) {
            auto kevent = static_cast<QKeyEvent *>(event);
            
    // NOTE: Unable to distinguish meta from other key combinations
    //       For example, Meta+S will still receive Meta release after
    //       fully releasing the key, actively detect whether there are
    //       other keys, and reset the state.
            // 座位特定的Meta键状态管理
        switch (kevent->key()) {
        case Qt::Key_Meta:
        case Qt::Key_Super_L:
                setSingleMetaKeyPendingPressed(seat, true);
            break;
        default:
                setSingleMetaKeyPendingPressed(seat, false);
            break;
    }

            // 调试功能快捷键应该总是最先处理
        if (QKeySequence(kevent->keyCombination())
            == QKeySequence(Qt::ControlModifier | Qt::ShiftModifier | Qt::MetaModifier | Qt::Key_F11)) {
            if (toggleDebugMenuBar())
                return true;
        }

            // 系统控制快捷键
        if (m_currentMode == CurrentMode::Normal
            && QKeySequence(kevent->modifiers() | kevent->key())
                == QKeySequence(Qt::ControlModifier | Qt::AltModifier | Qt::Key_Delete)) {
            setCurrentMode(CurrentMode::LockScreen);
            m_lockScreen->shutdown();
            setWorkspaceVisible(false);
            return true;
        }
            
            // 退出应用程序
        if (QKeySequence(kevent->modifiers() | kevent->key())
            == QKeySequence(Qt::META | Qt::Key_F12)) {
            qApp->quit();
            return true;
            } 
            // 截图选择器处理
            else if (m_captureSelector) {
            if (event->modifiers() == Qt::NoModifier && kevent->key() == Qt::Key_Escape)
                m_captureSelector->cancelSelection();
            } 
            // Meta键快捷键处理
            else if (event->modifiers() == Qt::MetaModifier) {
            const QList<Qt::Key> switchWorkspaceNums = { Qt::Key_1, Qt::Key_2, Qt::Key_3,
                                                         Qt::Key_4, Qt::Key_5, Qt::Key_6 };
                // 工作区切换 - 修改为座位特定的工作区切换
            if (kevent->key() == Qt::Key_Right) {
                restoreFromShowDesktop();
                // 为特定座位切换工作区
                workspace()->switchToNext();
                return true;
            } else if (kevent->key() == Qt::Key_Left) {
                restoreFromShowDesktop();
                // 为特定座位切换工作区
                workspace()->switchToPrev();
                return true;
            } else if (switchWorkspaceNums.contains(kevent->key())) {
                restoreFromShowDesktop();
                // 为特定座位切换到指定工作区
                int workspaceIndex = switchWorkspaceNums.indexOf(kevent->key());
                switchWorkspaceForSeat(seat, workspaceIndex);
                return true;
                } 
                // 多任务视图切换
                else if (kevent->key() == Qt::Key_S
                       && (m_currentMode == CurrentMode::Normal
                           || m_currentMode == CurrentMode::Multitaskview)) {
                restoreFromShowDesktop();
                if (m_multitaskView) {
                    m_multitaskView->toggleMultitaskView(IMultitaskView::ActiveReason::ShortcutKey);
                }
                return true;
#ifndef DISABLE_DDM
                } 
                // 锁屏
                else if (kevent->key() == Qt::Key_L) {
                if (m_lockScreen->isLocked()) {
                    return true;
                }

                showLockScreen();
                return true;
#endif
                } 
                // 显示桌面
                else if (kevent->key() == Qt::Key_D) {
                if (m_currentMode == CurrentMode::Multitaskview) {
                    return true;
                }
                if (m_showDesktop == WindowManagementV1::DesktopState::Normal)
                    m_windowManagement->setDesktopState(WindowManagementV1::DesktopState::Show);
                else if (m_showDesktop == WindowManagementV1::DesktopState::Show)
                    m_windowManagement->setDesktopState(WindowManagementV1::DesktopState::Normal);
                return true;
                } 
                // 窗口最大化
                else if (kevent->key() == Qt::Key_Up && m_activatedSurface) {
                m_activatedSurface->requestMaximize();
                return true;
                } 
                // 取消窗口最大化
                else if (kevent->key() == Qt::Key_Down && m_activatedSurface) {
                m_activatedSurface->requestCancelMaximize();
                return true;
            }
            } 
            // Alt键处理
            else if (kevent->key() == Qt::Key_Alt) {
            m_taskAltTimestamp = kevent->timestamp();
            m_taskAltCount = 0;
            } 
            // 任务切换器
            else if ((m_currentMode == CurrentMode::Normal
                    || m_currentMode == CurrentMode::WindowSwitch)
                   && (kevent->key() == Qt::Key_Tab || kevent->key() == Qt::Key_Backtab
                       || kevent->key() == Qt::Key_QuoteLeft
                       || kevent->key() == Qt::Key_AsciiTilde)) {
            if (event->modifiers().testFlag(Qt::AltModifier)) {
                int detal = kevent->timestamp() - m_taskAltTimestamp;
                if (detal < 150 && !kevent->isAutoRepeat()) {
                    auto current = Helper::instance()->workspace()->current();
                    Q_ASSERT(current);
                    auto next_surface = current->findNextActivedSurface();
                    if (next_surface)
                        Helper::instance()->forceActivateSurface(next_surface, Qt::TabFocusReason);
                    return true;
                }

                if (m_taskSwitch.isNull()) {
                    auto contentItem = window()->contentItem();
                    auto output = rootContainer()->primaryOutput();
                    m_taskSwitch = qmlEngine()->createTaskSwitcher(output, contentItem);

                        // 在任务切换器显示时恢复窗口的真实状态
                    restoreFromShowDesktop();
                    connect(m_taskSwitch,
                            SIGNAL(switchOnChanged()),
                            this,
                            SLOT(deleteTaskSwitch()));
                    m_taskSwitch->setZ(RootSurfaceContainer::OverlayZOrder);
                }

                if (kevent->isAutoRepeat()) {
                    m_taskAltCount++;
                } else {
                    m_taskAltCount = 3;
                }

                if (m_taskAltCount >= 3) {
                    m_taskAltCount = 0;
                    setCurrentMode(CurrentMode::WindowSwitch);
                    QString appid;
                    if (kevent->key() == Qt::Key_QuoteLeft || kevent->key() == Qt::Key_AsciiTilde) {
                        auto surface = Helper::instance()->activatedSurface();
                        if (surface) {
                            appid = surface->shellSurface()->appId();
                        }
                    }
                    auto filter = Helper::instance()->workspace()->currentFilter();
                    filter->setFilterAppId(appid);

                    if (event->modifiers() == Qt::AltModifier) {
                        QMetaObject::invokeMethod(m_taskSwitch, "next");
                        return true;
                    } else if (event->modifiers() == (Qt::AltModifier | Qt::ShiftModifier)
                               || event->modifiers()
                                   == (Qt::AltModifier | Qt::MetaModifier | Qt::ShiftModifier)) {
                        QMetaObject::invokeMethod(m_taskSwitch, "previous");
                        return true;
                    }
                }
            }
            } 
            // Alt + 其他键的组合
            else if (event->modifiers() == Qt::AltModifier) {
                // 关闭窗口
                if (kevent->key() == Qt::Key_F4 && m_activatedSurface) {
                m_activatedSurface->requestClose();
                return true;
            }
                // 显示窗口菜单
            if (kevent->key() == Qt::Key_Space && m_activatedSurface) {
                Q_EMIT m_activatedSurface->requestShowWindowMenu({ 0, 0 });
                return true;
            }
                // 任务切换器导航
            if (m_taskSwitch) {
                if (kevent->key() == Qt::Key_Left) {
                    QMetaObject::invokeMethod(m_taskSwitch, "previous");
                    return true;
                } else if (kevent->key() == Qt::Key_Right) {
                    QMetaObject::invokeMethod(m_taskSwitch, "next");
                    return true;
                }
            }
        }
    }
        // 处理键盘释放事件
        else if (event->type() == QEvent::KeyRelease) {
        auto kevent = static_cast<QKeyEvent *>(event);
            
            // Meta键释放处理
            if ((kevent->key() == Qt::Key_Meta || kevent->key() == Qt::Key_Super_L) 
                && getSingleMetaKeyPendingPressed(seat)) {
                // 单独按下Meta键的功能 - 可以在这里添加打开应用菜单等功能
                qDebug() << "Single Meta key pressed on seat:" << seat->name();
                // TODO: 添加打开应用菜单或其他功能
                setSingleMetaKeyPendingPressed(seat, false);
                return true;
            }
            
            // Alt键释放，退出任务切换器
            if (kevent->key() == Qt::Key_Alt && m_taskSwitch) {
                setCurrentMode(CurrentMode::Normal);
                QMetaObject::invokeMethod(m_taskSwitch, "exit");
        }
    }

        // 处理鼠标按钮事件
    if (event->type() == QEvent::MouseButtonPress || event->type() == QEvent::MouseButtonRelease) {
        handleLeftButtonStateChanged(event);
    }

        // 处理滚轮事件
    if (event->type() == QEvent::Wheel) {
        handleWhellValueChanged(event);
    }
    }

    // NOTE: Unable to distinguish meta from other key combinations
    //       For example, Meta+S will still receive Meta release after
    //       the QML multitask view component is loaded, thus it would close the component
    if (event->type() == QEvent::KeyRelease) {
        // Consume the meta release event when there is an alive task switch
        auto kevent = static_cast<QKeyEvent *>(event);
        if (kevent->key() == Qt::Key_Meta) {
            qDebug() << "KeyRelease event: " << kevent->key() << " taskSwitch:" << m_taskSwitch;
            if (m_taskSwitch)
                return true;
        }
    }

    // 以下是原有的事件处理逻辑
    if (event->type() == QEvent::MouseMove || event->type() == QEvent::MouseButtonPress) {
        seat->cursor()->setVisible(true);
    } else if (event->type() == QEvent::TouchBegin) {
        seat->cursor()->setVisible(false);
    }

    doGesture(event);

    // 修改：使用座位特定的移动拖拽逻辑，而不是全局的
    // 注意：使用前面已定义的eventSeat变量，不要重复定义
    if (auto surface = getMoveResizeSurfaceForSeat(eventSeat)) {
        // for move resize - 座位特定的处理
        if (Q_LIKELY(event->type() == QEvent::MouseMove || event->type() == QEvent::TouchUpdate)) {
            auto cursor = eventSeat ? eventSeat->cursor() : seat->cursor();
            Q_ASSERT(cursor);
            QMouseEvent *ev = static_cast<QMouseEvent *>(event);

            auto ownsOutput = surface->ownsOutput();
            if (!ownsOutput) {
                endMoveResizeForSeat(eventSeat);
                return false;
            }

            auto lastPosition = m_seatLastPressedPositions.value(eventSeat, cursor->position());
            auto increment_pos = ev->globalPosition() - lastPosition;
            
            // 更新最后位置
            m_seatLastPressedPositions[eventSeat] = ev->globalPosition();
            
            doMoveResizeForSeat(eventSeat, increment_pos);

            qDebug() << "Move/resize for seat:" << (eventSeat ? eventSeat->name() : "null") 
                     << "delta:" << increment_pos;

            return true;
        } else if (event->type() == QEvent::MouseButtonRelease
                   || event->type() == QEvent::TouchEnd) {
            endMoveResizeForSeat(eventSeat);
            qDebug() << "Ended move/resize for seat:" << (eventSeat ? eventSeat->name() : "null");
        }
    }
    
    // 为了兼容性，保留原有的全局移动拖拽处理（但只在没有座位特定处理时执行）
    if (!getMoveResizeSurfaceForSeat(eventSeat) && m_rootSurfaceContainer->moveResizeSurface()) {
        // 原有的全局处理逻辑...
        if (Q_LIKELY(event->type() == QEvent::MouseMove || event->type() == QEvent::TouchUpdate)) {
            auto cursor = seat->cursor();
            Q_ASSERT(cursor);
            QMouseEvent *ev = static_cast<QMouseEvent *>(event);

            auto surface = m_rootSurfaceContainer->moveResizeSurface();
            auto ownsOutput = surface->ownsOutput();
            if (!ownsOutput) {
                m_rootSurfaceContainer->endMoveResize();
                return false;
            }

            auto lastPosition =
                m_fakelastPressedPosition.value_or(cursor->lastPressedOrTouchDownPosition());
            auto increment_pos = ev->globalPosition() - lastPosition;
            m_rootSurfaceContainer->doMoveResize(increment_pos);

            return true;
        } else if (event->type() == QEvent::MouseButtonRelease
                   || event->type() == QEvent::TouchEnd) {
            m_rootSurfaceContainer->endMoveResize();
            m_fakelastPressedPosition.reset();
        }
    }

    // handle shortcut
    if (event->type() == QEvent::KeyPress) {
            auto kevent = static_cast<QKeyEvent *>(event);
        if (Q_UNLIKELY(event->spontaneous())) {
            // We should ignore the keypress events that send from Qt self,
            // for example, when opening a dialog by hotkey, it will send a return
            // key to the dialog as an accept signal.
            auto lastEventVariant = targetWindow->property("_lastInputEvent");
            if (lastEventVariant.isValid()) {
                // 简单的时间戳检查，避免QKeyEvent的复制构造问题
                auto lastTimestamp = targetWindow->property("_lastEventTimestamp").value<qint64>();
                if (qAbs(static_cast<qint64>(kevent->timestamp()) - lastTimestamp) < 50) {
                    // 可能是重复事件，忽略
                    targetWindow->setProperty("_lastInputEvent", QVariant());
                    return false;
                }
            }
            
            // 存储当前事件的时间戳
            targetWindow->setProperty("_lastEventTimestamp", static_cast<qint64>(kevent->timestamp()));
        } do {
            Q_ASSERT(targetWindow);
            if (targetWindow->property("_q_showWithoutActivating").toBool()) {
                break;
            }

            if (m_renderWindow != targetWindow && targetWindow->type() != Qt::ToolTip
                && !targetWindow->property("_isDialog").toBool()) {
                break;
            }

            // 获取当前键盘焦点的surface
            auto surface = keyboardFocusSurface();
            if (!surface) {
                break;
            }

            // 检查窗口和surface的激活状态是否一致
            // 简化逻辑，避免使用不存在的isActivated方法
            if (targetWindow->isActive() && surface != activatedSurface()) {
                qDebug() << "Activating surface for active window";
                this->activateSurface(surface, Qt::MouseFocusReason);
            }
        } while (false);
    }
      
      // 键盘事件的特殊处理 - 确保键盘事件被正确路由到对应座位的焦点窗口
      // 注意：使用前面已定义的eventSeat变量，不要重复定义
      if (event->type() == QEvent::KeyPress || event->type() == QEvent::KeyRelease) {
          // 记录键盘事件的详细信息，帮助调试
          if (event->type() == QEvent::KeyPress) {
              auto kevent = static_cast<QKeyEvent *>(event);
              qDebug() << "[KEY] KeyPress: seat=" << (eventSeat ? eventSeat->name() : "unknown") 
                       << "key=" << kevent->key() 
                       << "text=" << kevent->text();
              
              // 检查此座位当前的键盘焦点
              SurfaceWrapper *focusSurface = m_seatKeyboardFocusSurfaces.value(eventSeat);
              qDebug() << "[KEY] Current focus surface for seat" << (eventSeat ? eventSeat->name() : "unknown") 
                       << "is" << (focusSurface ? focusSurface->shellSurface()->appId() : "none");
              
              // 确保键盘事件被路由到正确的窗口
              if (eventSeat && focusSurface) {
                  // 验证键盘焦点是否与Wayland协议层面的焦点一致
                  if (eventSeat->keyboardFocusSurface() != focusSurface->surface()) {
                      qWarning() << "[KEY] Keyboard focus mismatch detected! Fixing...";
                      eventSeat->setKeyboardFocusSurface(focusSurface->surface());
                  }
                  
                  // 强制更新交互记录，确保后续事件正确路由
                  updateSurfaceSeatInteraction(focusSurface, eventSeat);
                  
                  // 确保焦点窗口处于激活状态
                  if (focusSurface->shellSurface()->hasCapability(WToplevelSurface::Capability::Focus)) {
                      focusSurface->setFocus(true, Qt::OtherFocusReason);
                      focusSurface->setActivate(true);
                  }
              } else if (eventSeat && !focusSurface) {
                  // 座位没有焦点表面，尝试查找适合的表面
                  qWarning() << "[KEY] Seat" << eventSeat->name() << "has no focus surface for keyboard event!";
                  
                  // 尝试查找此座位最近交互的表面
                  for (auto surface : m_rootSurfaceContainer->surfaces()) {
                      if (getLastInteractingSeat(surface) == eventSeat) {
                          qDebug() << "[KEY] Found recently interacted surface for seat" << eventSeat->name();
                          // 设置焦点
                          eventSeat->setKeyboardFocusSurface(surface->surface());
                          m_seatKeyboardFocusSurfaces[eventSeat] = surface;
                          surface->setFocus(true, Qt::OtherFocusReason);
                          break;
                      }
                  }
              }
          }
    }

    return false;
}

bool Helper::afterHandleEvent(WSeat *seat,
                              WSurface *watched,
                              QObject *surfaceItem,
                              QObject *eventObject,
                              QInputEvent *event)
{
    if (event->isSinglePointEvent()) {
        if (static_cast<QSinglePointEvent *>(event)->isBeginEvent()) {
        // surfaceItem is qml type: XdgSurfaceItem or LayerSurfaceItem
        auto toplevelSurface = qobject_cast<WSurfaceItem *>(surfaceItem)->shellSurface();
        if (!toplevelSurface)
            return false;
        Q_ASSERT(toplevelSurface->surface() == watched);

        auto surface = m_rootSurfaceContainer->getSurface(watched);
        
            // 获取事件对应的座位，并为该座位激活表面
        WSeat *eventSeat = getSeatForEvent(event);
            if (eventSeat && surface) {
                qDebug() << "[INPUT] Surface clicked: seat=" << eventSeat->name() 
                         << "surface=" << (surface->shellSurface() ? surface->shellSurface()->appId() : "unknown");
                
                // 设置座位特定的激活状态
                setActivatedSurfaceForSeat(eventSeat, surface);
                
                // 设置座位特定的键盘焦点 - 严格仅为产生事件的座位设置焦点
                if (surface->shellSurface()->hasCapability(WToplevelSurface::Capability::Focus) 
                    && surface->acceptKeyboardFocus()) {
                    // 直接设置焦点，绕过requestKeyboardFocusForSurfaceForSeat函数可能的副作用
                    if (eventSeat && eventSeat->nativeHandle()) {
                        // 记录这个表面最后是被哪个座位激活的 - 这很重要
        updateSurfaceSeatInteraction(surface, eventSeat);
        
                        // 直接设置UI焦点
                        surface->setFocus(true, Qt::MouseFocusReason);
                        
                        // 直接设置此座位的键盘焦点 - 添加额外的安全检查
                        // 检查surface和eventSeat的有效性
                        if (!surface->surface() || !eventSeat->nativeHandle()) {
                            qWarning() << "[INPUT] Invalid surface or seat native handle, skipping keyboard focus change";
                            return false;
                        }
                        
                        // 检查surface和其handle是否有效
                        if (!surface->surface() || !surface->surface()->handle()) {
                            qWarning() << "[INPUT] Surface is not valid for keyboard focus";
                            return false;
                        }
                        
                        // 检查座位的键盘设备是否可用
                        auto keyboard = eventSeat->keyboard();
                        if (!keyboard) {
                            qWarning() << "[INPUT] Seat has no keyboard device, skipping focus change";
                            return false;
                        }
                        
                        // 记录上次交互时间，用于改进输入法处理
                        surface->setProperty("lastInteractionTime", QDateTime::currentMSecsSinceEpoch());
                        
                        // 设置键盘焦点
                        eventSeat->setKeyboardFocusSurface(surface->surface());
                        m_seatKeyboardFocusSurfaces[eventSeat] = surface;
                        
                        qDebug() << "[INPUT] Set keyboard focus for seat" << eventSeat->name() << "to surface:" 
                                 << (surface->shellSurface() ? surface->shellSurface()->appId() : "unknown");
                        
                        // 记录当前的焦点状态
                        qDebug() << "[INPUT] Current keyboard focus state:";
                        for (auto s : m_seatManager->seats()) {
                            auto focusSurface = m_seatKeyboardFocusSurfaces.value(s);
                            qDebug() << "[INPUT]   - Seat:" << s->name() 
                                     << "focus:" << (focusSurface ? focusSurface->shellSurface()->appId() : "none");
                        }
                    }
                }
                
                // 更新UI状态，确保窗口显示正确的活动状态
                // 对于任何座位，我们都需要更新UI状态，但避免影响其他座位的键盘焦点
                if (surface->shellSurface()->hasCapability(WToplevelSurface::Capability::Focus)) {
                    // 对所有座位都相同：更新表面的UI状态
                    surface->setActivate(true);
                    surface->stackToLast();
                    workspace()->pushActivedSurface(surface);
                    
                    // 如果是主座位，也更新全局活动表面
                    if (eventSeat == m_seat) {
                        // 注意：这里使用setActivatedSurface而不是activateSurface
                        // 这样我们可以更新UI状态而不修改键盘焦点
                        setActivatedSurface(surface);
                    }
                }
            }
        }
    }

    return false;
}

bool Helper::unacceptedEvent(WSeat *seat, QWindow *, QInputEvent *event)
{
    if (event->isSinglePointEvent()) {
        if (static_cast<QSinglePointEvent *>(event)->isBeginEvent()) {
            // 获取事件对应的座位，并为该座位清除焦点
            WSeat *eventSeat = getSeatForEvent(event);
            
            if (eventSeat) {
                // 清除该座位的激活表面和键盘焦点
            setActivatedSurfaceForSeat(eventSeat, nullptr);
                requestKeyboardFocusForSurfaceForSeat(eventSeat, nullptr, Qt::OtherFocusReason);
                qDebug() << "Cleared activated surface and keyboard focus for seat:" << eventSeat->name();
            }
            
            // 为了兼容性，如果是主座位或未指定座位，也清除全局焦点
            if (!eventSeat || eventSeat == m_seat) {
                activateSurface(nullptr, Qt::OtherFocusReason);
            }
        }
    }

    return false;
}

bool Helper::doGesture(QInputEvent *event)
{
    if (event->type() == QEvent::NativeGesture) {
        auto e = static_cast<WGestureEvent *>(event);
        switch (e->gestureType()) {
        case Qt::BeginNativeGesture:
            if (e->libInputGestureType() == WGestureEvent::WLibInputGestureType::SwipeGesture)
                InputDevice::instance()->processSwipeStart(e->fingerCount());

            if (e->libInputGestureType() == WGestureEvent::WLibInputGestureType::HoldGesture)
                InputDevice::instance()->processHoldStart(e->fingerCount());
            break;
        case Qt::EndNativeGesture:
            if (e->libInputGestureType() == WGestureEvent::WLibInputGestureType::SwipeGesture) {
                if (e->cancelled())
                    InputDevice::instance()->processSwipeCancel();
                else
                    InputDevice::instance()->processSwipeEnd();
            }
            if (e->libInputGestureType() == WGestureEvent::WLibInputGestureType::HoldGesture)
                InputDevice::instance()->processHoldEnd();
            break;
        case Qt::PanNativeGesture:
            if (e->libInputGestureType() == WGestureEvent::WLibInputGestureType::SwipeGesture)
                InputDevice::instance()->processSwipeUpdate(e->delta());
        case Qt::ZoomNativeGesture:
        case Qt::SmartZoomNativeGesture:
        case Qt::RotateNativeGesture:
        case Qt::SwipeNativeGesture:
        default:
            break;
        }
    }
    return false;
}

Output *Helper::createNormalOutput(WOutput *output)
{
    Output *o = Output::create(output, qmlEngine(), this);
    auto future = QtConcurrent::run([o, this]() {
        if (isNvidiaCardPresent()) {
            o->outputItem()->setProperty("forceSoftwareCursor", true);
        }
    });
    o->outputItem()->stackBefore(m_rootSurfaceContainer);
    m_rootSurfaceContainer->addOutput(o);
    return o;
}

Output *Helper::createCopyOutput(WOutput *output, Output *proxy)
{
    return Output::createCopy(output, proxy, qmlEngine(), this);
}

QList<SurfaceWrapper *> Helper::getWorkspaceSurfaces(Output *filterOutput)
{
    QList<SurfaceWrapper *> surfaces;
    WOutputRenderWindow::paintOrderItemList(
        Helper::instance()->workspace(),
        [&surfaces, filterOutput](QQuickItem *item) -> bool {
            SurfaceWrapper *surfaceWrapper = qobject_cast<SurfaceWrapper *>(item);
            if (surfaceWrapper
                && (surfaceWrapper->showOnWorkspace(
                        Helper::instance()->workspace()->current()->id())
                    && (!filterOutput || surfaceWrapper->ownsOutput() == filterOutput))) {
                surfaces.append(surfaceWrapper);
                return true;
            } else {
                return false;
            }
        });

    return surfaces;
}

void Helper::moveSurfacesToOutput(const QList<SurfaceWrapper *> &surfaces,
                                  Output *targetOutput,
                                  Output *sourceOutput)
{
    if (!surfaces.isEmpty() && targetOutput) {
        const QRectF targetGeometry = targetOutput->geometry();

        for (auto *surface : surfaces) {
            if (!surface)
                continue;

            const QSizeF size = surface->size();
            QPointF newPos;

            if (surface->ownsOutput() == targetOutput) {
                newPos = surface->position();
            } else {
                const QRectF sourceGeometry =
                    sourceOutput ? sourceOutput->geometry() : surface->ownsOutput()->geometry();
                const QPointF relativePos = surface->position() - sourceGeometry.center();
                newPos = targetGeometry.center() + relativePos;
                surface->setOwnsOutput(targetOutput);
            }
            newPos.setX(
                qBound(targetGeometry.left(), newPos.x(), targetGeometry.right() - size.width()));
            newPos.setY(
                qBound(targetGeometry.top(), newPos.y(), targetGeometry.bottom() - size.height()));
            surface->setPosition(newPos);
        }
    }
}

SurfaceWrapper *Helper::keyboardFocusSurface() const
{
    auto item = m_renderWindow->activeFocusItem();
    if (!item)
        return nullptr;
    auto surface = qobject_cast<WSurfaceItem *>(item->parent());
    if (!surface)
        return nullptr;
    return qobject_cast<SurfaceWrapper *>(surface->parent());
}

void Helper::requestKeyboardFocusForSurface(SurfaceWrapper *newActivate, Qt::FocusReason reason)
{
    auto *nowKeyboardFocusSurface = keyboardFocusSurface();
    if (nowKeyboardFocusSurface == newActivate)
        return;

    Q_ASSERT(!newActivate
             || newActivate->shellSurface()->hasCapability(WToplevelSurface::Capability::Focus));

    if (nowKeyboardFocusSurface && nowKeyboardFocusSurface->hasActiveCapability()) {
        if (newActivate) {
            if (nowKeyboardFocusSurface->shellSurface()->keyboardFocusPriority()
                > newActivate->shellSurface()->keyboardFocusPriority())
                return;
        } else {
            if (nowKeyboardFocusSurface->shellSurface()->keyboardFocusPriority() > 0)
                return;
        }
    }

    if (nowKeyboardFocusSurface)
        nowKeyboardFocusSurface->setFocus(false, reason);

    if (newActivate)
        newActivate->setFocus(true, reason);
        
    // 在UI层面更新了焦点，但我们需要选择性地设置Wayland协议层面的键盘焦点
    // 找出最近与此表面交互的座位
    WSeat *interactingSeat = getLastInteractingSeat(newActivate);
    
    // 重要修复: 只设置主座位的焦点，其他座位保持各自独立的焦点
    // 这样可以确保多座位环境中，每个座位独立地控制其键盘焦点
    if (m_seat && m_seat->nativeHandle()) {
        m_seat->setKeyboardFocusSurface(newActivate ? newActivate->surface() : nullptr);
        m_seatKeyboardFocusSurfaces[m_seat] = newActivate;
        qDebug() << "Set keyboard focus for main seat" << m_seat->name() << "to surface:" 
                     << (newActivate ? newActivate->surface() : nullptr);
        }
    
    // 如果有明确的交互座位且不是主座位，更新该座位的记录(但不更改其当前焦点)
    if (interactingSeat && interactingSeat != m_seat && newActivate) {
        // 只更新交互记录，不设置键盘焦点
        updateSurfaceSeatInteraction(newActivate, interactingSeat);
    }
}

// 新增：针对特定座位设置键盘焦点
void Helper::requestKeyboardFocusForSurfaceForSeat(WSeat *seat, SurfaceWrapper *newActivate, Qt::FocusReason reason)
{
    if (!seat || !seat->nativeHandle()) {
        qWarning() << "[FOCUS] Cannot set keyboard focus for null or invalid seat";
        return;
    }
    
    // 获取当前座位的焦点表面
    auto *nowKeyboardFocusSurface = getKeyboardFocusSurfaceForSeat(seat);
    if (nowKeyboardFocusSurface == newActivate)
        return;
    
    qDebug() << "[FOCUS] requestKeyboardFocusForSurfaceForSeat: seat=" << seat->name()
             << "old_focus=" << (nowKeyboardFocusSurface ? nowKeyboardFocusSurface->shellSurface()->appId() : "none")
             << "new_focus=" << (newActivate ? newActivate->shellSurface()->appId() : "none");
    
    Q_ASSERT(!newActivate
             || newActivate->shellSurface()->hasCapability(WToplevelSurface::Capability::Focus));
    
    if (nowKeyboardFocusSurface && nowKeyboardFocusSurface->hasActiveCapability()) {
        if (newActivate) {
            if (nowKeyboardFocusSurface->shellSurface()->keyboardFocusPriority()
                > newActivate->shellSurface()->keyboardFocusPriority())
                return;
        } else {
            if (nowKeyboardFocusSurface->shellSurface()->keyboardFocusPriority() > 0)
                return;
        }
    }
    
    // 记录这是哪个座位激活的
    if (newActivate) {
        updateSurfaceSeatInteraction(newActivate, seat);
    }
    
    // 如果当前座位是主座位，更新UI焦点状态
    bool isMainSeat = (seat == m_seat);
    
    // 先取消旧表面的焦点状态（如果需要）
    if (nowKeyboardFocusSurface) {
        // 对于主座位，或者当没有任何座位拥有此表面的焦点时，清除UI焦点
        bool shouldClearUIFocus = isMainSeat;
        
        if (!isMainSeat) {
            // 检查其他座位是否仍然关注此表面
            bool otherSeatHasFocus = false;
            for (auto otherSeat : m_seatManager->seats()) {
                if (otherSeat != seat && getKeyboardFocusSurfaceForSeat(otherSeat) == nowKeyboardFocusSurface) {
                    otherSeatHasFocus = true;
                    break;
                }
            }
            
            // 如果没有其他座位关注此表面，也清除UI焦点
            shouldClearUIFocus = !otherSeatHasFocus;
        }
        
        if (shouldClearUIFocus) {
            nowKeyboardFocusSurface->setFocus(false, reason);
            qDebug() << "[FOCUS] Cleared UI focus for surface from seat" << seat->name();
        }
    }
    
    // 将当前座位的焦点状态记录在字典中
    if (newActivate) {
        m_seatKeyboardFocusSurfaces[seat] = newActivate;
    } else {
        m_seatKeyboardFocusSurfaces.remove(seat);
    }
    
    // 设置新表面的焦点状态 - 严格限制为单一座位
    if (newActivate) {
        // 更新UI焦点状态
        newActivate->setFocus(true, reason);
        qDebug() << "[FOCUS] Set UI focus for surface from seat" << seat->name();
        
        // 设置Wayland协议层面的键盘焦点 - 严格只为此座位设置焦点
        seat->setKeyboardFocusSurface(newActivate->surface());
        
        qDebug() << "[FOCUS] Set keyboard focus for seat" << seat->name() << "to surface:" 
                 << (newActivate->shellSurface() ? newActivate->shellSurface()->appId() : "unknown");
        
        // 记录这个表面最后是被哪个座位激活的 - 这是多座位系统的关键
        updateSurfaceSeatInteraction(newActivate, seat);
        
        // 记录当前的焦点状态
        qDebug() << "[FOCUS] Current keyboard focus state:";
        for (auto s : m_seatManager->seats()) {
            auto focusSurface = m_seatKeyboardFocusSurfaces.value(s);
            qDebug() << "[FOCUS]   - Seat:" << s->name() 
                     << "focus:" << (focusSurface ? focusSurface->shellSurface()->appId() : "none");
        }
    } else {
        // 清除此座位的键盘焦点 - 只影响此座位
        seat->setKeyboardFocusSurface(nullptr);
        qDebug() << "[FOCUS] Cleared keyboard focus for seat" << seat->name();
    }
}

// 新增：获取特定座位的焦点表面
SurfaceWrapper *Helper::getKeyboardFocusSurfaceForSeat(WSeat *seat) const
{
    if (!seat) return nullptr;
    return m_seatKeyboardFocusSurfaces.value(seat);
}

SurfaceWrapper *Helper::activatedSurface() const
{
    return m_activatedSurface;
}

void Helper::setActivatedSurface(SurfaceWrapper *newActivateSurface)
{
    if (m_activatedSurface == newActivateSurface)
        return;

    if (newActivateSurface) {
        Q_ASSERT(newActivateSurface->showOnWorkspace(workspace()->current()->id()));
        newActivateSurface->stackToLast();
        if (newActivateSurface->type() == SurfaceWrapper::Type::XWayland) {
            auto xwaylandSurface =
                qobject_cast<WXWaylandSurface *>(newActivateSurface->shellSurface());
            xwaylandSurface->restack(nullptr, WXWaylandSurface::XCB_STACK_MODE_ABOVE);
        }
    }

    if (m_activatedSurface)
        m_activatedSurface->setActivate(false);

    if (newActivateSurface) {
        if (m_showDesktop == WindowManagementV1::DesktopState::Show) {
            m_showDesktop = WindowManagementV1::DesktopState::Normal;
            m_windowManagement->setDesktopState(WindowManagementV1::DesktopState::Normal);
            newActivateSurface->setHideByShowDesk(true);
        }

        newActivateSurface->setActivate(true);
        workspace()->pushActivedSurface(newActivateSurface);
    }
    m_activatedSurface = newActivateSurface;
    Q_EMIT activatedSurfaceChanged();
}

void Helper::setCursorPosition(const QPointF &position)
{
    m_rootSurfaceContainer->endMoveResize();
    m_seat->setCursorPosition(position);
}

void Helper::handleRequestDrag([[maybe_unused]] WSurface *surface)
{
    m_seat->setAlwaysUpdateHoverTarget(true);

    struct wlr_drag *drag = m_seat->nativeHandle()->drag;
    Q_ASSERT(drag);
    QObject::connect(qw_drag::from(drag), &qw_drag::notify_drop, this, [this] {
        if (m_ddeShellV1)
            DDEActiveInterface::sendDrop(m_seat);
    });

    QObject::connect(qw_drag::from(drag), &qw_drag::before_destroy, this, [this, drag] {
        drag->data = NULL;
        m_seat->setAlwaysUpdateHoverTarget(false);
    });

    if (m_ddeShellV1)
        DDEActiveInterface::sendStartDrag(m_seat);
}

void Helper::handleLockScreen(LockScreenInterface *lockScreen)
{
    connect(lockScreen, &LockScreenInterface::shutdown, this, [this]() {
        if (m_lockScreen && currentMode() == Helper::CurrentMode::Normal) {
            setCurrentMode(CurrentMode::LockScreen);
            m_lockScreen->shutdown();
            setWorkspaceVisible(false);
        }
    });
    connect(lockScreen, &LockScreenInterface::lock, this, [this]() {
        if (m_lockScreen && currentMode() == Helper::CurrentMode::Normal) {
            setCurrentMode(CurrentMode::LockScreen);
            m_lockScreen->lock();
            setWorkspaceVisible(false);
        }
    });
    connect(lockScreen, &LockScreenInterface::switchUser, this, [this]() {
        if (m_lockScreen && currentMode() == Helper::CurrentMode::Normal) {
            setCurrentMode(CurrentMode::LockScreen);
            m_lockScreen->switchUser();
            setWorkspaceVisible(false);
        }
    });
}


void Helper::onSessionNew(const QString &sessionId, const QDBusObjectPath &sessionPath)
{
    const auto path = sessionPath.path();
    qCDebug(qLcHelper) << "Session new, sessionId:" << sessionId << ", sessionPath:" << path;
    QDBusConnection::systemBus().connect("org.freedesktop.login1", path, "org.freedesktop.login1.Session", "Lock", this, SLOT(onSessionLock()));
    QDBusConnection::systemBus().connect("org.freedesktop.login1", path, "org.freedesktop.login1.Session", "Unlock", this, SLOT(onSessionUnLock()));
}

void Helper::onSessionLock()
{
    showLockScreen();
}

void Helper::onSessionUnlock()
{
    if (m_lockScreen) {
        m_lockScreen->unlock();
    }
}

void Helper::allowNonDrmOutputAutoChangeMode(WOutput *output)
{
    output->safeConnect(&qw_output::notify_request_state,
                        this,
                        [this](wlr_output_event_request_state *newState) {
                            if (newState->state->committed & WLR_OUTPUT_STATE_MODE) {
                                auto output = qobject_cast<qw_output *>(sender());

                                output->commit_state(newState->state);
                            }
                        });
}

int Helper::indexOfOutput(WOutput *output) const
{
    for (int i = 0; i < m_outputList.size(); i++) {
        if (m_outputList.at(i)->output() == output)
            return i;
    }
    return -1;
}

Output *Helper::getOutput(WOutput *output) const
{
    for (auto o : std::as_const(m_outputList)) {
        if (o->output() == output)
            return o;
    }
    return nullptr;
}

void Helper::addOutput()
{
    qobject_cast<qw_multi_backend *>(m_backend->handle())
        ->for_each_backend(
            [](wlr_backend *backend, void *) {
                if (auto x11 = qw_x11_backend::from(backend)) {
                    qw_output::from(x11->output_create());
                } else if (auto wayland = qw_wayland_backend::from(backend)) {
                    qw_output::from(wayland->output_create());
                }
            },
            nullptr);
}

void Helper::setOutputMode(OutputMode mode)
{
    if (m_outputList.length() < 2 || m_mode == mode)
        return;
    m_mode = mode;
    Q_EMIT outputModeChanged();
    for (int i = 0; i < m_outputList.size(); i++) {
        if (m_outputList.at(i) == m_rootSurfaceContainer->primaryOutput())
            continue;
        Output *o;
        if (mode == OutputMode::Copy) {
            o = createCopyOutput(m_outputList.at(i)->output(),
                                 m_rootSurfaceContainer->primaryOutput());
            m_rootSurfaceContainer->removeOutput(m_outputList.at(i));
        } else if (mode == OutputMode::Extension) {
            o = createNormalOutput(m_outputList.at(i)->output());
            o->enable();
        }
        m_outputList.at(i)->deleteLater();
        m_outputList.replace(i, o);
    }
}

void Helper::setOutputProxy(Output *output) { }

float Helper::animationSpeed() const
{
    return m_animationSpeed;
}

void Helper::setAnimationSpeed(float newAnimationSpeed)
{
    if (qFuzzyCompare(m_animationSpeed, newAnimationSpeed))
        return;
    m_animationSpeed = newAnimationSpeed;
    Q_EMIT animationSpeedChanged();
}

Helper::OutputMode Helper::outputMode() const
{
    return m_mode;
}

void Helper::addSocket(WSocket *socket)
{
    m_server->addSocket(socket);
}

WXWayland *Helper::createXWayland()
{
    return m_shellHandler->createXWayland(m_server, m_seat, m_compositor, false);
}

void Helper::removeXWayland(WXWayland *xwayland)
{
    m_shellHandler->removeXWayland(xwayland);
}

WSocket *Helper::defaultWaylandSocket() const
{
    return m_socket;
}

WXWayland *Helper::defaultXWaylandSocket() const
{
    return m_defaultXWayland;
}

PersonalizationV1 *Helper::personalization() const
{
    return m_personalization;
}

bool Helper::toggleDebugMenuBar()
{
    bool ok = false;

    const auto outputs = rootContainer()->outputs();
    if (outputs.isEmpty())
        return false;

    bool firstOutputDebugMenuBarIsVisible = false;
    if (auto menuBar = outputs.first()->debugMenuBar()) {
        firstOutputDebugMenuBarIsVisible = menuBar->isVisible();
    }

    for (const auto &output : outputs) {
        if (output->debugMenuBar()) {
            output->debugMenuBar()->setVisible(!firstOutputDebugMenuBarIsVisible);
            ok = true;
        }
    }

    return ok;
}

QString Helper::cursorTheme() const
{
    return TreelandConfig::ref().cursorThemeName();
}

QSize Helper::cursorSize() const
{
    return TreelandConfig::ref().cursorSize();
}

WindowManagementV1::DesktopState Helper::showDesktopState() const
{
    return m_showDesktop;
}

bool Helper::isLaunchpad(WLayerSurface *surface) const
{
    if (!surface) {
        return false;
    }

    auto scope = QString(surface->handle()->handle()->scope);

    return scope == "dde-shell/launchpad";
}

void Helper::handleWindowPicker(WindowPickerInterface *picker)
{
    connect(picker, &WindowPickerInterface::pick, this, [this, picker](const QString &hint) {
        auto windowPicker =
            qobject_cast<WindowPicker *>(qmlEngine()->createWindowPicker(m_rootSurfaceContainer));
        windowPicker->setHint(hint);
        connect(windowPicker,
                &WindowPicker::windowPicked,
                this,
                [this, picker, windowPicker](WSurfaceItem *surfaceItem) {
                    if (surfaceItem) {
                        auto credentials = WClient::getCredentials(
                            surfaceItem->surface()->waylandClient()->handle());
                        picker->sendWindowPid(credentials->pid);
                        windowPicker->deleteLater();
                    }
                });
        connect(picker,
                &WindowPickerInterface::beforeDestroy,
                windowPicker,
                &WindowPicker::deleteLater);
    });
}

RootSurfaceContainer *Helper::rootSurfaceContainer() const
{
    return m_rootSurfaceContainer;
}

void Helper::setMultitaskViewImpl(IMultitaskView *impl)
{
    m_multitaskView = impl;
}

void Helper::setLockScreenImpl(ILockScreen *impl)
{
#ifndef DISABLE_DDM
    m_lockScreen = new LockScreen(impl, m_rootSurfaceContainer);
    m_lockScreen->setZ(RootSurfaceContainer::LockScreenZOrder);
    m_lockScreen->setVisible(false);

    for (auto *output : m_rootSurfaceContainer->outputs()) {
        m_lockScreen->addOutput(output);
    }

    if (auto primaryOutput = m_rootSurfaceContainer->primaryOutput()) {
        m_lockScreen->setPrimaryOutputName(primaryOutput->output()->name());
    }

    connect(m_lockScreen, &LockScreen::unlock, this, [this] {
        setCurrentMode(CurrentMode::Normal);
        setWorkspaceVisible(true);

        if (m_activatedSurface) {
            m_activatedSurface->setFocus(true, Qt::NoFocusReason);
        }
    });

    QDBusConnection::systemBus().connect("org.freedesktop.login1", "/org/freedesktop/login1", "org.freedesktop.login1.Manager", "SessionNew", this, SLOT(onSessionNew(const QString &,const QDBusObjectPath &)));

    if (CmdLine::ref().useLockScreen()) {
        showLockScreen();
    }
#endif
}

void Helper::setCurrentMode(CurrentMode mode)
{
    if (m_currentMode == mode)
        return;

    TreelandConfig::ref().setBlockActivateSurface(mode != CurrentMode::Normal);

    m_currentMode = mode;

    Q_EMIT currentModeChanged();
}

void Helper::showLockScreen()
{
    if (m_lockScreen->isLocked()) {
        return;
    }

    setCurrentMode(CurrentMode::LockScreen);
    m_lockScreen->lock();

    setWorkspaceVisible(false);

    if (m_multitaskView) {
        m_multitaskView->immediatelyExit();
    }

    deleteTaskSwitch();

    // send DDM switch to greeter mode
    // FIXME: DDM and Treeland should listen to the lock signal of login1
    QDBusInterface interface("org.freedesktop.DisplayManager",
                             "/org/freedesktop/DisplayManager/Seat0",
                             "org.freedesktop.DisplayManager.Seat",
                             QDBusConnection::systemBus());
    interface.asyncCall("SwitchToGreeter");
}

WSeat *Helper::seat() const
{
    return m_seat;
}

void Helper::handleLeftButtonStateChanged(const QInputEvent *event)
{
    Q_ASSERT(m_ddeShellV1 && m_seat);
    const QMouseEvent *me = static_cast<const QMouseEvent *>(event);
    if (me->button() == Qt::LeftButton) {
        if (event->type() == QEvent::MouseButtonPress) {
            DDEActiveInterface::sendActiveIn(DDEActiveInterface::Mouse, m_seat);
        } else {
            DDEActiveInterface::sendActiveOut(DDEActiveInterface::Mouse, m_seat);
        }
    }
}

void Helper::handleWhellValueChanged(const QInputEvent *event)
{
    Q_ASSERT(m_ddeShellV1 && m_seat);
    const QWheelEvent *we = static_cast<const QWheelEvent *>(event);
    QPoint delta = we->angleDelta();
    if (delta.x() + delta.y() < 0) {
        DDEActiveInterface::sendActiveOut(DDEActiveInterface::Wheel, m_seat);
    }
    if (delta.x() + delta.y() > 0) {
        DDEActiveInterface::sendActiveIn(DDEActiveInterface::Wheel, m_seat);
    }
}

void Helper::restoreFromShowDesktop(SurfaceWrapper *activeSurface)
{
    if (m_showDesktop == WindowManagementV1::DesktopState::Show) {
        m_showDesktop = WindowManagementV1::DesktopState::Normal;
        m_windowManagement->setDesktopState(WindowManagementV1::DesktopState::Normal);
        if (activeSurface) {
            activeSurface->requestCancelMinimize();
        }
        const auto &surfaces = getWorkspaceSurfaces();
        for (auto &surface : surfaces) {
            if (!surface->isMinimized() && !surface->isVisible()) {
                surface->setHideByShowDesk(true);
                surface->setSurfaceState(SurfaceWrapper::State::Minimized);
            }
        }
    }
}

Output *Helper::getOutputAtCursor() const
{
    QPoint cursorPos = QCursor::pos();
    for (auto output : m_outputList) {
        QRectF outputGeometry(output->outputItem()->position(), output->outputItem()->size());
        if (outputGeometry.contains(cursorPos)) {
            return output;
        }
    }

    return m_rootSurfaceContainer->primaryOutput();
}

void Helper::assignDeviceToSeat(WInputDevice *device)
{
    if (!device) {
        qWarning() << "Cannot assign null device to seat";
        return;
    }
    
    // 获取设备信息
    QString deviceName = device->name();
    WInputDevice::Type deviceType = device->type();
    
    // 设备类型合理性检查与过滤
    if (deviceType == WInputDevice::Type::Pointer && deviceName.contains("Keyboard", Qt::CaseInsensitive)) {
        qWarning() << "Device type mismatch - ignoring:" << deviceName;
        return;
    }
    
    if (deviceType == WInputDevice::Type::Keyboard && 
        (deviceName.contains("Mouse", Qt::CaseInsensitive) || deviceName.contains("Touchpad", Qt::CaseInsensitive))) {
        qWarning() << "Device type mismatch - ignoring:" << deviceName;
        return;
    }
    
    // 过滤系统按钮设备
    if (deviceType == WInputDevice::Type::Keyboard && 
        (deviceName.contains("Power Button") || deviceName.contains("Sleep Button") ||
         deviceName.contains("Lid Switch") || deviceName.contains("Video Bus"))) {
        return;
    }
    
    // 检查设备是否已分配
    for (auto seat : m_seatManager->seats()) {
        if (seat->deviceList().contains(device)) {
            return;  // 设备已分配，无需操作
        }
    }
    
    // 尝试自动分配设备
    bool assigned = m_seatManager->autoAssignDevice(device);
    if (!assigned && m_seat && m_seat->nativeHandle()) {
        // 自动分配失败，手动分配到默认座位
        m_seat->attachInputDevice(device);
    }
    
    // 查找设备最终被分配到的座位
    WSeat *assignedSeat = nullptr;
    for (auto seat : m_seatManager->seats()) {
        if (seat->deviceList().contains(device)) {
            assignedSeat = seat;
            break;
        }
    }
    
    if (!assignedSeat) {
        return;  // 设备未能分配给任何座位
    }
    
    // 确保座位有原生句柄
    if (!assignedSeat->nativeHandle()) {
        m_server->attach(assignedSeat);
    }
    
    // 如果是指针设备，确保座位有光标
    if (deviceType == WInputDevice::Type::Pointer && !assignedSeat->cursor()) {
        WCursor *cursor = new WCursor(assignedSeat);
        if (m_rootSurfaceContainer && m_rootSurfaceContainer->outputLayout()) {
            cursor->setLayout(m_rootSurfaceContainer->outputLayout());
        }
        assignedSeat->setCursor(cursor);
    }
    
    // 如果是键盘设备，确保座位有键盘焦点窗口
    if (deviceType == WInputDevice::Type::Keyboard && !assignedSeat->keyboardFocusWindow()) {
        assignedSeat->setKeyboardFocusWindow(m_renderWindow);
    }
}

void Helper::assignOutputToSeat(WOutput *output)
{
    if (!output)
        return;
        
    // 检查输出设备配置
    QString cache_location = QStandardPaths::writableLocation(QStandardPaths::AppConfigLocation);
    QSettings settings(cache_location + "/seats.ini", QSettings::IniFormat);
    qDebug() << "---------------assignOutputToSeat------- " <<cache_location ;
    
    QString outputName = output->name();
    settings.beginGroup("OutputAssignments");
    
    // 如果有明确的座位分配配置
    if (settings.contains(outputName)) {
        QString seatName = settings.value(outputName).toString();
        WSeat *seat = m_seatManager->getSeat(seatName);
        
        if (seat) {
            seat->attachOutput(output);
            qDebug() << "Output" << outputName << "assigned to seat" << seatName;
            settings.endGroup();
            return;
        }
    }
    settings.endGroup();
    
    // 如果没有明确的配置，分配到主座位
    m_seat->attachOutput(output);
    qDebug() << "Output" << outputName << "assigned to default seat" << m_seat->name();
}

void Helper::checkAndFixSeatDevices()
{
    // 确保每个座位有原生句柄
    for (auto seat : m_seatManager->seats()) {
        if (!seat->nativeHandle()) {
            m_server->attach(seat);
        }
    }
    
    // 确保所有设备已分配
    for (auto device : m_backend->inputDeviceList()) {
        bool isAssigned = false;
        for (auto seat : m_seatManager->seats()) {
            if (seat->deviceList().contains(device)) {
                isAssigned = true;
                break;
            }
        }
        
        if (!isAssigned) {
            assignDeviceToSeat(device);
        }
    }
    
    // 确保每个座位都有基本设备
    for (auto seat : m_seatManager->seats()) {
        bool hasPointer = false;
        bool hasKeyboard = false;
        
        // 检查座位的设备
        for (auto device : seat->deviceList()) {
            if (device->type() == WInputDevice::Type::Pointer) {
                hasPointer = true;
            } else if (device->type() == WInputDevice::Type::Keyboard) {
                hasKeyboard = true;
            }
        }
        
        // 修复指针设备
        if (!hasPointer) {
            // 特定分配规则
            if (seat->name() == "seat1") {
                // 先尝试分配特定设备
                for (auto device : m_backend->inputDeviceList()) {
                    if (device->type() == WInputDevice::Type::Pointer && 
                        device->name().contains("Logitech", Qt::CaseInsensitive)) {
                        seat->attachInputDevice(device);
                        hasPointer = true;
                        break;
                    }
                }
            }
            
            // 如果仍然缺少指针设备，尝试分配任何可用的设备
            if (!hasPointer) {
                for (auto device : m_backend->inputDeviceList()) {
                    if (device->type() == WInputDevice::Type::Pointer) {
                        bool assigned = false;
                        for (auto s : m_seatManager->seats()) {
                            if (s->deviceList().contains(device)) {
                                assigned = true;
                                break;
                            }
                        }
                        
                        if (!assigned) {
                            seat->attachInputDevice(device);
                            break;
                        }
                    }
                }
            }
        }
        
        // 修复键盘设备
        if (!hasKeyboard) {
            // 特定分配规则
            if (seat->name() == "seat1") {
                // 先尝试分配特定设备
                for (auto device : m_backend->inputDeviceList()) {
                    if (device->type() == WInputDevice::Type::Keyboard && 
                        device->name().contains("Lenovo", Qt::CaseInsensitive)) {
                        seat->attachInputDevice(device);
                        hasKeyboard = true;
                        break;
                    }
                }
            }
            
            // 如果仍然缺少键盘设备，尝试分配任何可用的设备
            if (!hasKeyboard) {
                for (auto device : m_backend->inputDeviceList()) {
                    if (device->type() == WInputDevice::Type::Keyboard) {
                        bool assigned = false;
                        for (auto s : m_seatManager->seats()) {
                            if (s != seat && s->deviceList().contains(device)) {
                                assigned = true;
                                break;
                            }
                        }
                        
                        if (!assigned) {
                            seat->attachInputDevice(device);
                            break;
                        }
                    }
                }
            }
        }
        
        // 确保有光标
        if (!seat->cursor() && std::any_of(seat->deviceList().begin(), seat->deviceList().end(),
                                          [](WInputDevice* device) { return device->type() == WInputDevice::Type::Pointer; })) {
            WCursor *cursor = new WCursor(seat);
        if (m_rootSurfaceContainer && m_rootSurfaceContainer->outputLayout()) {
            cursor->setLayout(m_rootSurfaceContainer->outputLayout());
        }
            seat->setCursor(cursor);
        }
    }
}

void Helper::setupSeatsConfiguration()
{
    for (auto seat : m_seatManager->seats()) {
        // 确保座位有原生句柄
        if (!seat->nativeHandle()) {
            m_server->attach(seat);
            if (!seat->nativeHandle()) {
                qWarning() << "Failed to create native handle for seat" << seat->name();
                continue;
            }
        }
        
        // 设置光标
        if (!seat->cursor()) {
            if (seat->name() == "seat0" && m_rootSurfaceContainer->cursor()) {
                seat->setCursor(m_rootSurfaceContainer->cursor());
            } else {
                WCursor *cursor = new WCursor(seat);
                if (m_rootSurfaceContainer->outputLayout()) {
                    cursor->setLayout(m_rootSurfaceContainer->outputLayout());
                }
                seat->setCursor(cursor);
            }
        }
        
        // 设置键盘焦点窗口
        seat->setKeyboardFocusWindow(m_renderWindow);
        
        // 设置事件过滤器
        if (!seat->eventFilter()) {
            seat->setEventFilter(this);
        }
        
        // 连接拖拽事件处理
        disconnect(seat, &WSeat::requestDrag, this, nullptr);
        connect(seat, &WSeat::requestDrag, this, [this, seat](WSurface *surface) {
            handleRequestDragForSeat(seat, surface);
        });
    }
}

void Helper::connectDeviceSignals()
{
    // 监听新设备添加
    connect(m_backend, &WBackend::inputAdded, this, [this](WInputDevice *device) {
        if (!device) return;
        
        // 将设备添加到合适的座位
        assignDeviceToSeat(device);
        
        // 如果是触摸板设备，初始化手势支持
        if (InputDevice::instance()->initTouchPad(device)) {
            if (m_windowGesture) {
                m_windowGesture->addTouchpadSwipeGesture(SwipeGesture::Up, 3);
                m_windowGesture->addTouchpadHoldGesture(3);
            }

            if (m_multiTaskViewGesture) {
                m_multiTaskViewGesture->addTouchpadSwipeGesture(SwipeGesture::Up, 4);
                m_multiTaskViewGesture->addTouchpadSwipeGesture(SwipeGesture::Right, 4);
            }
        }
    });
}

void Helper::assignExistingDevices()
{
    // 分配现有设备到座位
    for (auto device : m_backend->inputDeviceList()) {
        assignDeviceToSeat(device);
    }
}

bool Helper::getSingleMetaKeyPendingPressed(WSeat *seat) const
{
    return m_seatMetaKeyStates.value(seat, false);
}

void Helper::setSingleMetaKeyPendingPressed(WSeat *seat, bool pressed)
{
    if (!seat) return;
    
    if (pressed) {
        m_seatMetaKeyStates[seat] = true;
    } else {
        m_seatMetaKeyStates.remove(seat);
    }
}

WSeat *Helper::getSeatForEvent(QInputEvent *event) const
{
    // 从事件设备获取座位信息
    if (event->device()) {
        WInputDevice *device = WInputDevice::from(event->device());
        if (device) {
            WSeat *deviceSeat = device->seat();
            if (deviceSeat) {
                return deviceSeat;
            }
        }
    }
    
    // 没有设备信息时，返回主座位
    return m_seat;
}

void Helper::setActivatedSurfaceForSeat(WSeat *seat, SurfaceWrapper *surface)
{
    if (!seat) return;
    
    auto currentSurface = m_seatActivatedSurfaces.value(seat);
    if (currentSurface == surface) {
            return;
        }
        
    // 取消当前座位激活的表面
    if (currentSurface) {
        currentSurface->setActivate(false);
    }
    
    // 设置新的激活表面
        if (surface) {
            surface->setActivate(true);
            surface->stackToLast();
            workspace()->pushActivedSurface(surface);
        }
        
        m_seatActivatedSurfaces[seat] = surface;
    
    // 如果是主座位，同时更新全局激活表面
    if (seat == m_seat) {
        m_activatedSurface = surface;
        Q_EMIT activatedSurfaceChanged();
    }
}

SurfaceWrapper *Helper::getActivatedSurfaceForSeat(WSeat *seat) const
{
    if (!seat) return nullptr;
    return m_seatActivatedSurfaces.value(seat);
}

void Helper::handleRequestDragForSeat(WSeat *seat, WSurface *surface)
{
    if (!seat || !seat->nativeHandle()) return;
    
    qDebug() << "Handle drag request for seat:" << seat->name();
    
    seat->setAlwaysUpdateHoverTarget(true);
    
    struct wlr_drag *drag = seat->nativeHandle()->drag;
    Q_ASSERT(drag);
    
    QObject::connect(qw_drag::from(drag), &qw_drag::notify_drop, this, [this, seat] {
        if (m_ddeShellV1)
            DDEActiveInterface::sendDrop(seat);
    });
    
    QObject::connect(qw_drag::from(drag), &qw_drag::before_destroy, this, [this, seat, drag] {
        drag->data = NULL;
        seat->setAlwaysUpdateHoverTarget(false);
    });
    
    if (m_ddeShellV1)
        DDEActiveInterface::sendStartDrag(seat);
}

void Helper::beginMoveResizeForSeat(WSeat *seat, SurfaceWrapper *surface, Qt::Edges edges)
{
    if (!seat || !surface) return;
    
    // 如果这个座位已经有正在进行的拖拽操作，先结束之前的
    auto currentSurface = m_seatMoveResizeSurfaces.value(seat);
    if (currentSurface && currentSurface != surface) {
        endMoveResizeForSeat(seat);
    }
    
    // 确保全局的拖拽操作也被结束
    if (m_rootSurfaceContainer->moveResizeSurface() != nullptr) {
        m_rootSurfaceContainer->endMoveResize();
    }
    
    // 设置新的移动/调整大小状态
    m_seatMoveResizeSurfaces[seat] = surface;
    m_seatMoveResizeEdges[seat] = edges;
    
    // 记录当前光标位置作为起始位置
    if (seat->cursor()) {
        m_seatLastPressedPositions[seat] = seat->cursor()->position();
    }
}

void Helper::endMoveResizeForSeat(WSeat *seat)
{
    if (!seat) return;
    
    if (m_seatMoveResizeSurfaces.contains(seat)) {
        m_seatMoveResizeSurfaces.remove(seat);
        m_seatMoveResizeEdges.remove(seat);
        m_seatLastPressedPositions.remove(seat);
    }
}

SurfaceWrapper *Helper::getMoveResizeSurfaceForSeat(WSeat *seat) const
{
    if (!seat) return nullptr;
    return m_seatMoveResizeSurfaces.value(seat);
}

void Helper::doMoveResizeForSeat(WSeat *seat, const QPointF &delta)
{
    if (!seat) return;
    
    auto surface = m_seatMoveResizeSurfaces.value(seat);
    if (!surface) return;
    
    auto edges = m_seatMoveResizeEdges.value(seat);
    
    if (edges == Qt::Edges{}) {
        // 移动模式
        auto newPos = surface->position() + delta;
        surface->setPosition(newPos);
    } else {
        // 调整大小模式
        auto currentGeometry = surface->geometry();
        auto newGeometry = currentGeometry;
        
        if (edges & Qt::LeftEdge) {
            newGeometry.setLeft(currentGeometry.left() + delta.x());
        }
        if (edges & Qt::RightEdge) {
            newGeometry.setRight(currentGeometry.right() + delta.x());
        }
        if (edges & Qt::TopEdge) {
            newGeometry.setTop(currentGeometry.top() + delta.y());
        }
        if (edges & Qt::BottomEdge) {
            newGeometry.setBottom(currentGeometry.bottom() + delta.y());
        }
        
        // 确保大小合理
        if (newGeometry.width() < 50) {
            newGeometry.setWidth(50);
        }
        if (newGeometry.height() < 50) {
            newGeometry.setHeight(50);
        }
        
        // 设置位置和大小
        surface->setPosition(newGeometry.topLeft());
        surface->setSize(newGeometry.size());
    }
}

WSeat *Helper::getLastInteractingSeat(SurfaceWrapper *surface) const
{
    if (!surface) return nullptr;
    
    auto lastSeatVariant = surface->property("lastInteractingSeat");
    if (lastSeatVariant.isValid()) {
        auto seat = lastSeatVariant.value<WSeat*>();
        // 验证座位仍然有效
        if (m_seatManager->seats().contains(seat)) {
                return seat;
        }
    }
    
    return nullptr;
}

void Helper::updateSurfaceSeatInteraction(SurfaceWrapper *surface, WSeat *seat)
{
    if (!surface || !seat) return;
    
    // 设置新的交互座位和时间
    surface->setProperty("lastInteractingSeat", QVariant::fromValue(seat));
    surface->setProperty("lastInteractionTime", QDateTime::currentMSecsSinceEpoch());
}

// 多座位工作区切换
void Helper::switchWorkspaceForSeat(WSeat *seat, int index)
{
    if (!seat) return;
    
    // 切换工作区
    workspace()->switchTo(index);
}
