.vscode/
.cache/
build*/

*.user

# Compiled Object files
*.slo
*.lo
*.o

# Compiled Dynamic libraries
*.so
*.dylib

# Compiled Static libraries
*.lai
*.la
*.a

# nix
.direnv/
result
result-*

# clangd
compile_commands.json

# executeable files
*.qm

# cmake
CMakeLists.txt.user

#ccls
.ccls-cache

# qtcreator
*.autosave

.qmlls.ini

# dpkg-buildpackage
debian/.debhelper/
debian/tmp/
debian/treeland*/
debian/debhelper-build-stamp
debian/files
debian/*.substvars
debian/*.debhelper.log
obj-*-linux-gnu/
