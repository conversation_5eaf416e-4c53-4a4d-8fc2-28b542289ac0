# This is the clang-format style used by qt releated deepin projects.
#
# This file is modified from https://github.com/qt/qt5/blob/5b22f8ec2e4fbb58e362b709ee82f2dbd8afdfd3/_clang-format
# based on the rules from https://github.com/linuxdeepin/deepin-styleguide,
# https://wiki.qt.io/Qt_Coding_Style and https://wiki.qt.io/Coding_Conventions
#
# This is for clang-format >= 14.0.0

---
# Webkit style was loosely based on the Qt style
BasedOnStyle: WebKit

# deepin project might not use same standard.
Standard: Auto

# Column width is limited to 100 in accordance with Qt Coding Style.
# https://wiki.qt.io/Qt_Coding_Style
# Note that this may be changed at some point in the future.
ColumnLimit: 100
# How much weight do extra characters after the line length limit have.
# PenaltyExcessCharacter: 4

# Disable reflow of some specific comments
# qdoc comments: indentation rules are different.
# Translation comments and SPDX license identifiers are also excluded.
CommentPragmas: "^!|^:|^ SPDX-License-Identifier:"

PointerAlignment: Right

# We use template< without space.
SpaceAfterTemplateKeyword: false

# We want to break before the operators, but not before a '='.
BreakBeforeBinaryOperators: NonAssignment

# Braces are usually attached, but not after functions or class declarations.
BreakBeforeBraces: Custom
BraceWrapping:
  AfterClass: true
  AfterControlStatement: false
  AfterEnum: true
  AfterFunction: true
  AfterNamespace: false
  AfterObjCDeclaration: false
  AfterStruct: true
  AfterUnion: true
  BeforeCatch: false
  BeforeElse: false
  IndentBraces: false

# Indent initializers by 4 spaces
ConstructorInitializerIndentWidth: 4

# Indent width for line continuations.
ContinuationIndentWidth: 4

# No indentation for namespaces.
NamespaceIndentation: None

# Allow indentation for preprocessing directives (if/ifdef/endif). https://reviews.llvm.org/rL312125
IndentPPDirectives: AfterHash
# We only indent with 2 spaces for preprocessor directives
PPIndentWidth: 2

# Horizontally align arguments after an open bracket.
# The coding style does not specify the following, but this is what gives
# results closest to the existing code.
AlignAfterOpenBracket: Align
AlwaysBreakTemplateDeclarations: true

# As clang-format 13 can regroup includes we enable this feature.
# basically according to https://wiki.qt.io/Coding_Conventions#Including_headers
# and https://github.com/linuxdeepin/deepin-styleguide
IncludeBlocks: Regroup
IncludeCategories:
  # gtest/gmock's h files
  - Regex: '^<g(test|mock)/'
    Priority: 1
    CaseSensitive: true
  # Your project's h files.
  - Regex: '^"'
    Priority: 2
  # Waylib libraries' h files
  - Regex: '^<W[A-Z]'
    Priority: 4
  # qwroots libraries' h files
  - Regex: '^<qw[a-z]'
    Priority: 5
    CaseSensitive: true
  # DTK libraries' h files
  - Regex: '^<D[A-Z]'
    Priority: 6
    CaseSensitive: true
  # QT libraries' h files
  - Regex: '^<(Q[A-Z]|Qt)'
    Priority: 7
  # C++ system headers (as of C++23).
  - Regex: '^<(algorithm|any|array|atomic|barrier|bit|bitset|cassert|ccomplex|cctype|cerrno|cfenv|cfloat|charconv|chrono|cinttypes|ciso646|climits|clocale|cmath|codecvt|compare|complex|concepts|condition_variable|coroutine|csetjmp|csignal|cstdalign|cstdarg|cstdbool|cstddef|cstdint|cstdio|cstdlib|cstring|ctgmath|ctime|cuchar|cwchar|cwctype|deque|exception|execution|expected|filesystem|flat_map|flat_set|format|forward_list|fstream|functional|future|generator|initializer_list|iomanip|ios|iosfwd|iostream|istream|iterator|latch|limits|list|locale|map|mdspan|memory|memory_resource|mutex|new|numbers|numeric|optional|ostream|print|queue|random|ranges|ratio|regex|scoped_allocator|semaphore|set|shared_mutex|source_location|span|spanstream|sstream|stack|stacktrace|stdexcept|stdfloat|stop_token|streambuf|string|string_view|strstream|syncstream|system_error|thread|tuple|type_traits|typeindex|typeinfo|unordered_map|unordered_set|utility|valarray|variant|vector|version)>$'
    Priority: 8
    CaseSensitive: true
  # C system headers.
  - Regex: '^<(aio|arpa/inet|assert|complex|cpio|ctype|curses|dirent|dlfcn|errno|fcntl|fenv|float|fmtmsg|fnmatch|ftw|glob|grp|iconv|inttypes|iso646|langinfo|libgen|limits|locale|math|monetary|mqueue|ndbm|netdb|net/if|netinet/in|netinet/tcp|nl_types|poll|pthread|pwd|regex|sched|search|semaphore|setjmp|signal|spawn|stdalign|stdarg|stdatomic|stdbool|stddef|stdint|stdio|stdlib|stdnoreturn|string|strings|stropts|sys/ipc|syslog|sys/mman|sys/msg|sys/resource|sys/select|sys/sem|sys/shm|sys/socket|sys/stat|sys/statvfs|sys/time|sys/times|sys/types|sys/uio|sys/un|sys/utsname|sys/wait|tar|term|termios|tgmath|threads|time|trace|uchar|ulimit|uncntrl|unistd|utime|utmpx|wchar|wctype|wordexp)\.h>$'
    Priority: 8
    CaseSensitive: true
  # other libraries' h files.
  - Regex: '^<'
    Priority: 3
IncludeIsMainRegex: '((T|t)est)?$'
SortIncludes: CaseSensitive

# macros for which the opening brace stays attached.
ForEachMacros:   [ foreach, Q_FOREACH, BOOST_FOREACH, forever, Q_FOREVER, QBENCHMARK, QBENCHMARK_ONCE ]

# Break constructor initializers before the colon and after the commas.
BreakConstructorInitializers: BeforeComma
BreakInheritanceList: BeforeComma

# Add "// namespace <namespace>" comments on closing brace for a namespace
# Ignored for namespaces that qualify as a short namespace,
# see 'ShortNamespaceLines'
FixNamespaceComments: true

# Definition of how short a short namespace is, default 1
ShortNamespaceLines: 1

# When escaping newlines in a macro attach the '\' as far left as possible, e.g.
##define a     \
#   something; \
#   other;     \
#   thelastlineislong;
AlignEscapedNewlines: Left

# Avoids the addition of a space between an identifier and the
# initializer list in list-initialization.
SpaceBeforeCpp11BracedList: false

AllowAllArgumentsOnNextLine: false
AllowShortLambdasOnASingleLine: Empty
AllowShortFunctionsOnASingleLine: Empty
AllowAllParametersOfDeclarationOnNextLine: false
AlignTrailingComments: true
BinPackArguments: false
BinPackParameters: false
PackConstructorInitializers: Never
PenaltyReturnTypeOnItsOwnLine: 130
SeparateDefinitionBlocks: Always
WhitespaceSensitiveMacros:
  - Q_PROPERTY
  - Q_INTERFACES
