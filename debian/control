Source: treeland
Priority: optional
Maintainer: rewine <<EMAIL>>
Build-Depends: cmake (>= 3.4~),
               debhelper-compat (= 13),
               extra-cmake-modules (>= 1.4.0~),
               libddm-dev,
               libdtk6core-dev,
               libdtk6declarative-dev,
               libdtk6gui-dev,
               libdtk6systemsettings-dev,
               libdtkcommon-dev,
               libsystemd-dev [linux-any],
               systemd,
               libwaylib-dev,
               libwlroots-0.18-dev,
               pkg-config,
               qt6-base-dev (>= 6.6.1~),
               qt6-base-private-dev (>= 6.6.1~),
               qt6-declarative-dev (>= 6.6.1~),
               qt6-shadertools-dev,
               qt6-tools-dev,
               qt6-tools-dev-tools (>= 6.6.1~),
               qt6-wayland,
               qt6-wayland-dev,
               qt6-wayland-private-dev,
               qwlroots,
               wlr-protocols,
               treeland-protocols,
               libpam0g-dev,
               libjemalloc-dev,
Standards-Version: 4.6.0
Section: dde
Homepage: https://github.com/linuxdeepin/treeland.git
#Vcs-Browser: https://salsa.debian.org/debian/treeland
#Vcs-Git: https://salsa.debian.org/debian/treeland.git
Rules-Requires-Root: no

Package: treeland
Section: dde
Architecture: any
Multi-Arch: same
Depends: qml6-module-qtquick-layouts,
         qml6-module-qtquick-particles,
         treeland-data (= ${source:Version}),
         xwayland,
         ${shlibs:Depends},
Description: a Wayland compositor based on wlroots and QML, designed with an attractive and elegant appearance.

Package: treeland-dev
Section: libdevel
Architecture: any
Multi-Arch: same
Depends: ${misc:Depends}, treeland( =${source:Version}),
Description: a Wayland compositor based on wlroots and QML, designed with an attractive and elegant appearance.

Package: treeland-examples
Section: dde
Architecture: any
Multi-Arch: same
Depends: ${shlibs:Depends},
Description: examples for treeland.

Package: treeland-data
Section: dde
Architecture: all
Depends: ${misc:Depends},
Description: data files of treeland.

Package: treeland-wayland-session
Section: dde
Architecture: all
Depends: ${misc:Depends},
         treeland (= ${source:Version}),
Description: treeland user session files of treeland.
