Format: https://www.debian.org/doc/packaging-manuals/copyright-format/1.0/
Upstream-Name: treeland
Source: https://github.com/linuxdeepin/treeland

Files: .github/* .obs/*.yml garnix.yaml
Copyright: None
License: CC0-1.0

Files: .clang-format
  .editorconfig
  *.frag
  *.ui
  *.vert
  **/qmldir
Copyright: None
License: CC0-1.0

Files: debian/* rpm/* archlinux/*
Copyright: None
License: CC0-1.0

Files: *.nix .envrc flake.lock renovate.json
Copyright: None
License: CC0-1.0

Files: translations/* po/* *.qm *.ts
Copyright: UnionTech Software Technology Co., Ltd.
License: GPL-2.0-only

Files: *.svg *.png *.jpg *.jpeg *.webp *.dci *.gif
Copyright: UnionTech Software Technology Co., Ltd.
License: CC0-1.0

Files: *.json
  *.service
  *.txt
  *.sh
  *.html
  .gitignore
  .gitmodules
  INSTALL.md
  *.desktop
  *.css
  *.ttf
  *.conf
  *.xml
  .release.json
Copyright: UnionTech Software Technology Co., Ltd.
License: CC0-1.0

Files: *.md
  README.md
  README.zh_CN.md
  CONTRIBUTING.md
  ChangeLog
  CONTRIBUTORS
Copyright: UnionTech Software Technology Co., Ltd.
License: CC-BY-4.0

Files: *.cmake
  CMakeLists.txt
  *.in *.qrc
  misc/*
  services/*
  releng/create_changelog.pl
Copyright: None
License: GPL-2.0-only

Files: src/greeter/*.h src/greeter/*.cpp
Copyright: None
License: GPL-2.0-only

Files: src/*.h src/*.cpp src/*.qml tests/*.cpp tests/*.h tests/*.qml
Copyright: UnionTech Software Technology Co., Ltd.
License: Apache-2.0 OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
