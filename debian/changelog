treeland (0.5.21) unstable; urgency=medium

  * fix: inputted password and capsIndicator button are covered

 -- rewine <<EMAIL>>  Thu, 17 Apr 2025 15:17:48 +0800

treeland (0.5.20) unstable; urgency=medium

  * fix: activeColor not saved to dconfig
  * fix: send error output enter for layer shell
  * chore: input popup should not use radius

 -- rewine <<EMAIL>>  Mon, 10 Mar 2025 16:20:35 +0800

treeland (0.5.19) unstable; urgency=medium

  * fix: can't set output's postion
  * fix: layer shell's popup not enter popup container

 -- rewine <<EMAIL>>  Sat, 15 Feb 2025 15:30:10 +0800

treeland (0.5.18) unstable; urgency=medium

  * fix: the tooltip text may disappear unexpectedly
  * fix: can't start on VirtualBox
  * feat: cache outputs settings

 -- rewine <<EMAIL>>  Fri, 14 Feb 2025 10:31:38 +0800

treeland (0.5.17) unstable; urgency=medium

  * fix: nix ci for build
  * Fix missing treeland's translations

 -- <PERSON><PERSON> <<EMAIL>>  Wed, 15 Jan 2025 11:02:18 +0800

treeland (0.5.16) unstable; urgency=medium

  * fix: crashed at accessing surface
  * Fix crash at QStyleHints

 -- zhangkun <<EMAIL>>  Tue, 14 Jan 2025 16:16:43 +0800

treeland (0.5.15) unstable; urgency=medium

  * chore: update default window corner size

 -- zhangkun <<EMAIL>>  Tue, 14 Jan 2025 11:45:30 +0800

treeland (0.5.14) unstable; urgency=medium

  * Fix crash at PersonalizationAttached::noTitlebar
  * Add a background color for DockPreview

 -- JiDe Zhang <<EMAIL>> Mon, 13 Jan 2025 18:13:21 +0800

treeland (0.5.13) unstable; urgency=medium

  * feat: add ctrl-alt-del shortcut key
  * Add TREELAND_SESSION_ENVIRONMENTS env
  * fix: popup of screen recorder is below it's parent
  * Fix typo
  * Ignore window rounded radius when maximized

 -- JiDe Zhang <<EMAIL>> Mon, 13 Jan 2025 11:02:32 +0800

treeland (0.5.12) unstable; urgency=medium

  * Fix maybe freeze when display DockPreview
  * feat: update default values
  * fix: adjust UI for greeter

 -- Groveer <<EMAIL>>  Wed, 08 Jan 2025 11:22:31 +0800

treeland (0.5.11) unstable; urgency=medium

  * bump version to 0.5.11

 -- Groveer <<EMAIL>>  Wed, 25 Dec 2024 18:47:45 +0800

treeland (0.5.10) unstable; urgency=medium

  * bump version to 0.5.10

 -- Groveer <<EMAIL>>  Fri, 20 Dec 2024 16:04:58 +0800

treeland (0.5.9) unstable; urgency=medium

  * bump version to 0.5.9

 -- Groveer <<EMAIL>>  Tue, 17 Dec 2024 21:04:15 +0800

treeland (0.5.8) unstable; urgency=medium

  * bump version to 0.5.8

 -- Groveer <<EMAIL>>  Fri, 13 Dec 2024 14:14:01 +0800

treeland (0.5.7) unstable; urgency=medium

  * bump version to 0.5.7

 -- Groveer <<EMAIL>>  Tue, 10 Dec 2024 18:29:02 +0800

treeland (0.5.6) unstable; urgency=medium

  * bump version to 0.5.6

 -- rewine <<EMAIL>>  Fri, 06 Dec 2024 15:05:05 +0800

treeland (0.5.5) unstable; urgency=medium

  * bump version to 0.5.5

 -- Groveer <<EMAIL>>  Thu, 05 Dec 2024 21:22:32 +0800

treeland (0.5.4) unstable; urgency=medium

  * bump version to 0.5.4

 -- Groveer <<EMAIL>>  Sat, 30 Nov 2024 15:52:51 +0800

treeland (0.5.3) unstable; urgency=medium

  * bump version to 0.5.3

 -- Groveer <<EMAIL>>  Fri, 29 Nov 2024 15:58:26 +0800

treeland (0.5.2) unstable; urgency=medium

  * bump version to 0.5.2

 -- Groveer <<EMAIL>>  Tue, 26 Nov 2024 15:01:56 +0800

treeland (0.5.1) unstable; urgency=medium

  * bump version to 0.5.1

 -- Groveer <<EMAIL>>  Fri, 22 Nov 2024 18:10:04 +0800

treeland (0.5.0) unstable; urgency=medium

  * bump version to 0.5.0

 -- Groveer <<EMAIL>>  Thu, 21 Nov 2024 17:02:20 +0800

treeland (0.4.4) unstable; urgency=medium

  * bump version to 0.4.4

 -- Groveer <<EMAIL>>  Fri, 15 Nov 2024 14:15:57 +0800

treeland (0.4.3) unstable; urgency=medium

  * bump version to 0.4.3

 -- Groveer <<EMAIL>>  Mon, 11 Nov 2024 19:10:14 +0800

treeland (0.4.1) unstable; urgency=medium

  * update appearance protocol

 -- Dingyuan Zhang <<EMAIL>>  Thu, 01 Nov 2024 11:20:00 +0800

treeland (0.4) unstable; urgency=medium

  * update workspace

 -- Dingyuan Zhang <<EMAIL>>  Thu, 01 Nov 2024 11:20:00 +0800

treeland (0.3) unstable; urgency=medium

  * support windows animation

 -- Dingyuan Zhang <<EMAIL>>  Thu, 25 Jul 2024 18:20:00 +0800

treeland (0.2.3) unstable; urgency=medium

  * refactor protocol from qml to c++

 -- Dingyuan Zhang <<EMAIL>>  Thu, 27 Jun 2024 14:27:40 +0800

treeland (0.2.2) unstable; urgency=medium

  * fix user list not refresh
  * fix greeter in multi output

 -- Dingyuan Zhang <<EMAIL>>  Fri, 26 Jan 2024 15:18:40 +0800

treeland (0.2.1) unstable; urgency=medium

  * fix cannot set wallpaper

 -- Dingyuan Zhang <<EMAIL>>  Thu, 25 Jan 2024 09:29:40 +0800

treeland (0.2.0) unstable; urgency=medium

  * Support dock preview
  * New shortcut config
  * Add back to normal function

 -- Dingyuan Zhang <<EMAIL>>  Mon, 8 Jan 2024 18:37:40 +0800

treeland (0.1.1) unstable; urgency=medium

  * fix user session not set XDG_CURRENT_DESKTOP

 -- Dingyuan Zhang <<EMAIL>>  Tue, 25 Dec 2023 13:07:40 +0800

treeland (0.1.0) unstable; urgency=medium

  * Initial release

 -- Dingyuan Zhang <<EMAIL>>  Tue, 12 Dec 2023 13:07:40 +0800
