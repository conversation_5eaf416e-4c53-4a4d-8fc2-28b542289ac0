<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1" language="en_US">
<context>
    <name>WindowMenu</name>
    <message>
        <location filename="../src/core/qml/WindowMenu.qml" line="29"/>
        <source>Minimize</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../src/core/qml/WindowMenu.qml" line="34"/>
        <source>Unmaximize</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../src/core/qml/WindowMenu.qml" line="34"/>
        <source>Maximize</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../src/core/qml/WindowMenu.qml" line="39"/>
        <source>Move</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../src/core/qml/WindowMenu.qml" line="44"/>
        <source>Resize</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../src/core/qml/WindowMenu.qml" line="50"/>
        <source>Always on Top</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../src/core/qml/WindowMenu.qml" line="56"/>
        <source>Always on Visible Workspace</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../src/core/qml/WindowMenu.qml" line="70"/>
        <source>Move to Left Work Space</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../src/core/qml/WindowMenu.qml" line="77"/>
        <source>Move to Right Work Space</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../src/core/qml/WindowMenu.qml" line="83"/>
        <source>Close</source>
        <translation type="unfinished"></translation>
    </message>
</context>
</TS>
