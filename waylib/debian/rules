#!/usr/bin/make -f
DPKG_EXPORT_BUILDFLAGS = 1

# see FEATURE AREAS in dpkg-buildflags(1)
export DEB_BUILD_MAINT_OPTIONS = hardening=+all

include /usr/share/dpkg/default.mk
export QT_SELECT = qt6

%:
	dh $@ --parallel

override_dh_auto_configure:
	dh_auto_configure -- -DCMAKE_INSTALL_PREFIX=/usr

#override_dh_auto_test:
#	echo "skip auto test"

override_dh_shlibdeps:
	dh_shlibdeps --dpkg-shlibdeps-params=--ignore-missing-info
