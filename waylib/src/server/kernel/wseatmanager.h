#pragma once

#include <WServer>
#include <WOutput>
#include <WSeat>
#include <QObject>
#include <QMap>
#include <QRegularExpression>
#include <QJsonObject>

QT_BEGIN_NAMESPACE
class QJsonObject;
QT_END_NAMESPACE

WAYLIB_SERVER_BEGIN_NAMESPACE

class WInputDevice;

class WAYLIB_SERVER_EXPORT WSeatManager : public QObject, public WServerInterface
{
    Q_OBJECT

public:
    explicit WSeatManager(QObject *parent = nullptr);
    ~WSeatManager();

    // 座位管理
    WSeat *createSeat(const QString &name, bool isFallback = false);
    void removeSeat(const QString &name);
    void removeSeat(WSeat *seat);
    WSeat *getSeat(const QString &name) const;
    QList<WSeat*> seats() const;
    WSeat *fallbackSeat() const;
    
    // 设备分配
    void assignDeviceToSeat(WInputDevice *device, const QString &seatName);
    bool autoAssignDevice(WInputDevice *device);
    
    // 输出设备关联
    void assignOutputToSeat(WOutput *output, const QString &seatName);
    
    // 配置管理
    void loadConfig(const QJsonObject &config);
    QJsonObject saveConfig() const;
    
    // 设备匹配规则
    void addDeviceRule(const QString &seatName, const QString &rule);
    void removeDeviceRule(const QString &seatName, const QString &rule);
    QStringList deviceRules(const QString &seatName) const;

    // 设备自动分配
    bool deviceMatchesSeat(WInputDevice *device, WSeat *seat) const;

    // 获取与设备匹配的座位
    WSeat *findSeatForDevice(WInputDevice *device) const;
    
    // 获取与输出设备匹配的座位
    WSeat *findSeatForOutput(WOutput *output) const;

protected:
    void create(WServer *server) override;
    void destroy(WServer *server) override;
    wl_global *global() const override;
    QByteArrayView interfaceName() const override;
    
private:
    QMap<QString, WSeat*> m_seats;
    QMap<QString, QList<QRegularExpression>> m_deviceRules;
    QString m_fallbackSeatName;
};

WAYLIB_SERVER_END_NAMESPACE
