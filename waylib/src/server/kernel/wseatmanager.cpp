// Copyright (C) 2023 Ji<PERSON><PERSON> <<EMAIL>>.
// SPDX-License-Identifier: Apache-2.0 OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only

#include "wseatmanager.h"
#include "wseat.h"
#include "winputdevice.h"
#include "woutput.h"

#include <qwinputdevice.h>
// #include <qwcommon.h>

#include <QJsonObject>
#include <QJsonArray>
#include <QDebug>

WAYLIB_SERVER_BEGIN_NAMESPACE

WSeatManager::WSeatManager(QObject *parent)
    : QObject(parent)
{
}

WSeatManager::~WSeatManager()
{
    qDeleteAll(m_seats);
}

WSeat *WSeatManager::createSeat(const QString &name, bool isFallback)
{
    if (m_seats.contains(name))
        return m_seats[name];
        
    WSeat *seat = new WSeat(name);
    seat->setIsFallback(isFallback);
    m_seats[name] = seat;
    
    if (isFallback) {
        m_fallbackSeatName = name;
        // 确保只有一个fallback座位
        for (auto it = m_seats.begin(); it != m_seats.end(); ++it) {
            if (it.key() != name && it.value()->isFallback()) {
                it.value()->setIsFallback(false);
            }
        }
    }
    
    return seat;
}

void WSeatManager::removeSeat(const QString &name)
{
    if (m_seats.contains(name)) {
        WSeat *seat = m_seats.take(name);
        
        // 如果移除的是fallback座位，需要指定一个新的
        if (seat->isFallback() && !m_seats.isEmpty()) {
            auto it = m_seats.begin();
            it.value()->setIsFallback(true);
            m_fallbackSeatName = it.key();
        }
        
        // 移除座位前先分离所有设备
        QList<WInputDevice*> devices = seat->deviceList();
        for (auto device : devices) {
            seat->detachInputDevice(device);
            
            // 重新分配设备到其他座位
            if (!m_seats.isEmpty()) {
                autoAssignDevice(device);
            }
        }
        
        // 移除座位前先分离所有输出设备
        QList<WOutput*> outputs = seat->outputs();
        for (auto output : outputs) {
            seat->detachOutput(output);
            
            // 重新分配输出设备到其他座位
            if (!m_seats.isEmpty() && fallbackSeat()) {
                fallbackSeat()->attachOutput(output);
            }
        }
        
        delete seat;
    }
}

void WSeatManager::removeSeat(WSeat *seat)
{
    if (!seat)
        return;
        
    // 找到座位名称
    QString seatName;
    for (auto it = m_seats.begin(); it != m_seats.end(); ++it) {
        if (it.value() == seat) {
            seatName = it.key();
            break;
        }
    }
    
    if (!seatName.isEmpty()) {
        removeSeat(seatName);
    } else {
        qWarning() << "Attempted to remove a seat that is not managed by WSeatManager";
    }
}

WSeat *WSeatManager::getSeat(const QString &name) const
{
    return m_seats.value(name);
}

QList<WSeat*> WSeatManager::seats() const
{
    return m_seats.values();
}

WSeat *WSeatManager::fallbackSeat() const
{
    return m_seats.value(m_fallbackSeatName);
}

void WSeatManager::assignDeviceToSeat(WInputDevice *device, const QString &seatName)
{
    if (!device) {
        qWarning() << "Cannot assign null device to seat";
        return;
    }
    
    // 首先检查设备是否已经分配给了某个座位
    for (auto seat : m_seats) {
        if (seat->deviceList().contains(device)) {
            if (seat->name() == seatName) {
                // 设备已经在正确的座位上
                return;
            }
            
            // 从当前座位移除设备
            seat->detachInputDevice(device);
            break;
        }
    }
    
    // 将设备分配给指定的座位
    if (m_seats.contains(seatName)) {
        m_seats[seatName]->attachInputDevice(device);
    } else if (fallbackSeat()) {
        // 如果指定的座位不存在，分配给fallback座位
        fallbackSeat()->attachInputDevice(device);
    }
}

bool WSeatManager::autoAssignDevice(WInputDevice *device)
{
    if (!device) {
        qWarning() << "Cannot auto-assign null device";
        return false;
    }
    
    // 首先检查设备是否已经分配给了某个座位
    for (auto seat : m_seats) {
        if (seat->deviceList().contains(device)) {
            // 设备已经分配，不需要再次分配
            return true;
        }
    }
    
    // 查找匹配的座位
    WSeat *targetSeat = findSeatForDevice(device);
    
    if (targetSeat) {
        targetSeat->attachInputDevice(device);
        return true;
    } else if (fallbackSeat()) {
        // 如果没有匹配的规则，分配给fallback座位
        fallbackSeat()->attachInputDevice(device);
        return true;
    }
    
    return false;
}

void WSeatManager::assignOutputToSeat(WOutput *output, const QString &seatName)
{
    if (!output) {
        qWarning() << "Cannot assign null output to seat";
        return;
    }
    
    // 首先检查输出设备是否已经分配给了某个座位
    for (auto seat : m_seats) {
        if (seat->outputs().contains(output)) {
            if (seat->name() == seatName) {
                // 输出设备已经在正确的座位
                return;
            }
            
            // 从当前座位移除输出设备
            seat->detachOutput(output);
            break;
        }
    }
    
    // 将输出设备分配给指定的座位
    if (m_seats.contains(seatName)) {
        m_seats[seatName]->attachOutput(output);
    } else if (fallbackSeat()) {
        // 如果指定的座位不存在，分配给fallback座位
        fallbackSeat()->attachOutput(output);
    }
}

void WSeatManager::addDeviceRule(const QString &seatName, const QString &rule)
{
    if (!m_seats.contains(seatName)) {
        qWarning() << "Cannot add device rule for non-existent seat:" << seatName;
        return;
    }
    
    QRegularExpression regex(rule);
    if (!regex.isValid()) {
        qWarning() << "Invalid regex pattern for device rule:" << rule;
        return;
    }
    
    if (!m_deviceRules.contains(seatName)) {
        m_deviceRules[seatName] = QList<QRegularExpression>();
    }
    
        m_deviceRules[seatName].append(regex);
}

void WSeatManager::removeDeviceRule(const QString &seatName, const QString &rule)
{
    if (!m_deviceRules.contains(seatName))
        return;
        
    QRegularExpression regex(rule);
    if (!regex.isValid())
        return;
        
        auto &rules = m_deviceRules[seatName];
    for (int i = 0; i < rules.size(); i++) {
        if (rules[i].pattern() == regex.pattern()) {
                rules.removeAt(i);
                break;
        }
    }
    
    if (rules.isEmpty())
        m_deviceRules.remove(seatName);
}

QStringList WSeatManager::deviceRules(const QString &seatName) const
{
    if (!m_deviceRules.contains(seatName))
        return QStringList();
        
    QStringList result;
    for (const auto &regex : m_deviceRules[seatName]) {
        result.append(regex.pattern());
        }
    
    return result;
}

void WSeatManager::loadConfig(const QJsonObject &config)
{
    // 清除现有配置
    QStringList seatNames = m_seats.keys();
    for (const auto &name : seatNames) {
        removeSeat(name);
    }
    m_seats.clear();
    m_deviceRules.clear();
    
    // 加载座位配置
    QJsonArray seatsArray = config["seats"].toArray();
    for (const auto &seatValue : seatsArray) {
        QJsonObject seatObj = seatValue.toObject();
        QString name = seatObj["name"].toString();
        bool isFallback = seatObj["fallback"].toBool();
        
        WSeat *seat = createSeat(name, isFallback);
        
        // 加载设备规则
        QJsonArray rulesArray = seatObj["deviceRules"].toArray();
        for (const auto &ruleValue : rulesArray) {
            QString rule = ruleValue.toString();
            addDeviceRule(name, rule);
        }
        
        // 输出设备和工作区需要在系统启动后匹配
    }
    
    // 如果没有座位，创建默认座位
    if (m_seats.isEmpty()) {
        createSeat("seat0", true);
    }
    
    // 如果没有fallback座位，指定第一个座位为fallback
    if (!fallbackSeat() && !m_seats.isEmpty()) {
        auto it = m_seats.begin();
        it.value()->setIsFallback(true);
        m_fallbackSeatName = it.key();
    }
}

QJsonObject WSeatManager::saveConfig() const
{
    QJsonObject config;
    QJsonArray seatsArray;
    
    for (auto it = m_seats.begin(); it != m_seats.end(); ++it) {
        QString name = it.key();
        WSeat *seat = it.value();
        
        QJsonObject seatObj;
        seatObj["name"] = name;
        seatObj["fallback"] = seat->isFallback();
        
        // 保存设备规则
        QJsonArray rulesArray;
        for (const auto &rule : deviceRules(name)) {
            rulesArray.append(rule);
        }
        seatObj["deviceRules"] = rulesArray;
        
        // 保存输出设备
        QJsonArray outputsArray;
        for (auto output : seat->outputs()) {
            outputsArray.append(output->name());
        }
        seatObj["outputs"] = outputsArray;
        
        seatsArray.append(seatObj);
    }
    
    config["seats"] = seatsArray;
    return config;
}

void WSeatManager::create(WServer *server)
{
    qInfo() << "Creating all seats...";
    
    if (!server) {
        qCritical() << "Cannot create seats with null server";
        return;
    }
    
    // 创建所有座位
    for (auto it = m_seats.begin(); it != m_seats.end(); ++it) {
        WSeat *seat = it.value();
        qInfo() << "Creating seat:" << seat->name();
        
        // 将座位附加到服务器
        server->attach(seat);
        
        // 验证座位是否有原生句柄
        if (!seat->nativeHandle()) {
            qCritical() << "Failed to create native handle for seat" << seat->name();
        } else {
            qInfo() << "Successfully created native handle for seat" << seat->name();
        }
    }
    
    // 确保至少有一个 fallback 座位
    if (!fallbackSeat() && !m_seats.isEmpty()) {
        auto it = m_seats.begin();
        it.value()->setIsFallback(true);
        m_fallbackSeatName = it.key();
        qInfo() << "Set" << it.key() << "as fallback seat";
    }
}

void WSeatManager::destroy(WServer *server)
{
    // 清理所有座位
    for (auto seat : m_seats) {
        // 座位会自动从server中移除，不需要显式调用
    }
}

bool WSeatManager::deviceMatchesSeat(WInputDevice *device, WSeat *seat) const
{
    if (!device || !seat)
        return false;
        
    // 获取座位名称
    QString seatName = seat->name();
    
    // 如果没有这个座位的规则，返回false
    if (!m_deviceRules.contains(seatName)) {
        qDebug() << "Seat" << seatName << "has no device rules";
        return false;
    }
        
    // 获取设备信息进行调试 - 使用真实设备名称
    QString deviceName = device->name();
    WInputDevice::Type deviceType = device->type();
    
    qDebug() << "Checking device" << deviceName << "type:" << static_cast<int>(deviceType) 
             << "against rules for seat" << seatName;
    
    // 打印所有规则
    for (const auto &rule : m_deviceRules[seatName]) {
        qDebug() << "  Rule:" << rule.pattern();
    }
        
    // 检查设备是否匹配任何规则
    bool matches = seat->matchesDevice(device, m_deviceRules[seatName]);
    qDebug() << "Device" << deviceName << (matches ? "matches" : "does not match") << "seat" << seatName;
    return matches;
}

WSeat *WSeatManager::findSeatForDevice(WInputDevice *device) const
{
    if (!device)
        return nullptr;
        
    QString deviceName = device->name();
    WInputDevice::Type deviceType = device->type();
    
    qDebug() << "Finding seat for device:" << deviceName << "type:" << static_cast<int>(deviceType);
        
    // 首先检查设备是否已经分配给了某个座位
    for (auto seat : m_seats) {
        if (seat->deviceList().contains(device)) {
            qDebug() << "Device" << deviceName << "already assigned to seat" << seat->name();
            return seat;
        }
    }
    
    // 然后检查设备是否匹配任何座位的规则
    // 重要：非fallback座位优先匹配，这样更具体的规则会先生效
    qDebug() << "Checking device against seat rules...";
    
    // 第一轮：检查非fallback座位
    for (auto it = m_seats.begin(); it != m_seats.end(); ++it) {
        WSeat *seat = it.value();
        if (seat->isFallback()) {
            continue; // 跳过fallback座位
        }
        qDebug() << "Checking non-fallback seat:" << seat->name();
        if (deviceMatchesSeat(device, seat)) {
            qDebug() << "Device" << deviceName << "matched non-fallback seat" << seat->name();
            return seat;
        }
    }
    
    // 第二轮：检查fallback座位
    WSeat *fallbackSeat = this->fallbackSeat();
    if (fallbackSeat) {
        qDebug() << "Checking fallback seat:" << fallbackSeat->name();
        
        // 对于fallback座位，我们有两种情况:
        // 1. 如果fallback座位有规则，只有匹配规则的设备才分配给它
        // 2. 如果fallback座位没有规则，任何设备都可以分配给它
        
        bool hasRules = m_deviceRules.contains(fallbackSeat->name()) && 
                       !m_deviceRules[fallbackSeat->name()].isEmpty();
        
        if (hasRules) {
            // fallback座位有规则，检查是否匹配
            if (deviceMatchesSeat(device, fallbackSeat)) {
                qDebug() << "Device" << deviceName << "matched fallback seat" << fallbackSeat->name();
                return fallbackSeat;
            }
        } else {
            // fallback座位没有规则，所有设备都分配给它
            qDebug() << "Fallback seat" << fallbackSeat->name() << "has no rules, assigning all unmatched devices to it";
            return fallbackSeat;
        }
    }
    
    // 如果没有匹配的规则，返回fallback座位作为最后的选择
    qDebug() << "Device" << deviceName << "did not match any seat rules, using fallback seat as last resort";
    if (fallbackSeat) {
        qDebug() << "Final fallback seat:" << fallbackSeat->name();
    } else {
        qDebug() << "No fallback seat available!";
    }
    return fallbackSeat;
}

WSeat *WSeatManager::findSeatForOutput(WOutput *output) const
{
    if (!output)
        return nullptr;
        
    // 检查输出设备是否已经分配给了某个座位
    for (auto seat : m_seats) {
        if (seat->outputs().contains(output)) {
            return seat;
        }
    }
    
    // 如果没有分配，返回fallback座位
    return fallbackSeat();
}

wl_global *WSeatManager::global() const
{
    // WSeatManager不需要创建全局对象，因为它只是管理多个WSeat
    return nullptr;
}

QByteArrayView WSeatManager::interfaceName() const
{
    // WSeatManager不是Wayland协议的一部分，所以没有接口名
    return "wseatmanager";
}

WAYLIB_SERVER_END_NAMESPACE
