<?xml version="1.0" encoding="UTF-8"?>

<!--
SPDX-License-Identifier: HPND
-->

<protocol name="text_input_unstable_v2">
  <copyright>
    Copyright © 2012, 2013 Intel Corporation
    Copyright © 2015, 2016 Jan <PERSON><PERSON>

    Permission to use, copy, modify, distribute, and sell this
    software and its documentation for any purpose is hereby granted
    without fee, provided that the above copyright notice appear in
    all copies and that both that copyright notice and this permission
    notice appear in supporting documentation, and that the name of
    the copyright holders not be used in advertising or publicity
    pertaining to distribution of the software without specific,
    written prior permission.  The copyright holders make no
    representations about the suitability of this software for any
    purpose.  It is provided "as is" without express or implied
    warranty.

    THE COPYRIGHT HOLDERS DISCLAIM ALL WARRANTIES WITH REGARD TO THIS
    SOFTWARE, INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND
    FITNESS, IN NO EVENT SHALL THE COPYRIGHT HOLDERS BE LIABLE FOR ANY
    SPECIAL, INDIRECT OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
    WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN
    AN ACTION OF CONTRACT, <PERSON><PERSON><PERSON><PERSON>ENCE OR OTHER TORTIOUS ACTION,
    ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF
    THIS SOFTWARE.
  </copyright>

  <interface name="zwp_text_input_v2" version="1">
    <description summary="text input">
      The zwp_text_input_v2 interface represents text input and input methods
      associated with a seat. It provides enter/leave events to follow the
      text input focus for a seat.

      Requests are used to enable/disable the text-input object and set
      state information like surrounding and selected text or the content type.
      The information about the entered text is sent to the text-input object
      via the pre-edit and commit events. Using this interface removes the need
      for applications to directly process hardware key events and compose text
      out of them.

      Text is valid UTF-8 encoded, indices and lengths are in bytes. Indices
      have to always point to the first byte of an UTF-8 encoded code point.
      Lengths are not allowed to contain just a part of an UTF-8 encoded code
      point.

      State is sent by the state requests (set_surrounding_text,
      set_content_type, set_cursor_rectangle and set_preferred_language) and
      an update_state request. After an enter or an input_method_change event
      all state information is invalidated and needs to be resent from the
      client. A reset or entering a new widget on client side also
      invalidates all current state information.
    </description>

    <request name="destroy" type="destructor">
      <description summary="Destroy the wp_text_input">
	Destroy the wp_text_input object. Also disables all surfaces enabled
	through this wp_text_input object
      </description>
    </request>

    <request name="enable">
      <description summary="enable text input for surface">
	Enable text input in a surface (usually when a text entry inside of it
	has focus).

	This can be called before or after a surface gets text (or keyboard)
	focus via the enter event. Text input to a surface is only active
	when it has the current text (or keyboard) focus and is enabled.
      </description>
      <arg name="surface" type="object" interface="wl_surface"/>
    </request>

    <request name="disable">
      <description summary="disable text input for surface">
	Disable text input in a surface (typically when there is no focus on any
	text entry inside the surface).
      </description>
      <arg name="surface" type="object" interface="wl_surface"/>
    </request>

    <request name="show_input_panel">
      <description summary="show input panels">
	Requests input panels (virtual keyboard) to show.

	This should be used for example to show a virtual keyboard again
	(with a tap) after it was closed by pressing on a close button on the
	keyboard.
      </description>
    </request>

    <request name="hide_input_panel">
      <description summary="hide input panels">
	Requests input panels (virtual keyboard) to hide.
      </description>
    </request>

    <request name="set_surrounding_text">
      <description summary="sets the surrounding text">
	Sets the plain surrounding text around the input position. Text is
	UTF-8 encoded. Cursor is the byte offset within the surrounding text.
	Anchor is the byte offset of the selection anchor within the
	surrounding text. If there is no selected text, anchor is the same as
	cursor.

	Make sure to always send some text before and after the cursor
	except when the cursor is at the beginning or end of text.

	When there was a configure_surrounding_text event take the
	before_cursor and after_cursor arguments into account for picking how
	much surrounding text to send.

	There is a maximum length of wayland messages so text can not be
	longer than 4000 bytes.
      </description>
      <arg name="text" type="string"/>
      <arg name="cursor" type="int"/>
      <arg name="anchor" type="int"/>
    </request>

    <enum name="content_hint" bitfield="true">
      <description summary="content hint">
	Content hint is a bitmask to allow to modify the behavior of the text
	input.
      </description>
      <entry name="none" value="0x0" summary="no special behaviour"/>
      <entry name="auto_completion" value="0x1" summary="suggest word completions"/>
      <entry name="auto_correction" value="0x2" summary="suggest word corrections"/>
      <entry name="auto_capitalization" value="0x4" summary="switch to uppercase letters at the start of a sentence"/>
      <entry name="lowercase" value="0x8" summary="prefer lowercase letters"/>
      <entry name="uppercase" value="0x10" summary="prefer uppercase letters"/>
      <entry name="titlecase" value="0x20" summary="prefer casing for titles and headings (can be language dependent)"/>
      <entry name="hidden_text" value="0x40" summary="characters should be hidden"/>
      <entry name="sensitive_data" value="0x80" summary="typed text should not be stored"/>
      <entry name="latin" value="0x100" summary="just latin characters should be entered"/>
      <entry name="multiline" value="0x200" summary="the text input is multiline"/>
    </enum>

    <enum name="content_purpose">
      <description summary="content purpose">
	The content purpose allows to specify the primary purpose of a text
	input.

	This allows an input method to show special purpose input panels with
	extra characters or to disallow some characters.
      </description>
      <entry name="normal" value="0" summary="default input, allowing all characters"/>
      <entry name="alpha" value="1" summary="allow only alphabetic characters"/>
      <entry name="digits" value="2" summary="allow only digits"/>
      <entry name="number" value="3" summary="input a number (including decimal separator and sign)"/>
      <entry name="phone" value="4" summary="input a phone number"/>
      <entry name="url" value="5" summary="input an URL"/>
      <entry name="email" value="6" summary="input an email address"/>
      <entry name="name" value="7" summary="input a name of a person"/>
      <entry name="password" value="8" summary="input a password (combine with password or sensitive_data hint)"/>
      <entry name="date" value="9" summary="input a date"/>
      <entry name="time" value="10" summary="input a time"/>
      <entry name="datetime" value="11" summary="input a date and time"/>
      <entry name="terminal" value="12" summary="input for a terminal"/>
    </enum>

    <request name="set_content_type">
      <description summary="set content purpose and hint">
	Sets the content purpose and content hint. While the purpose is the
	basic purpose of an input field, the hint flags allow to modify some
	of the behavior.

	When no content type is explicitly set, a normal content purpose with
	none hint should be assumed.
      </description>
      <arg name="hint" type="uint" enum="content_hint"/>
      <arg name="purpose" type="uint" enum="content_purpose"/>
    </request>

    <request name="set_cursor_rectangle">
      <description summary="set cursor position">
	Sets the cursor outline as a x, y, width, height rectangle in surface
	local coordinates.

	Allows the compositor to put a window with word suggestions near the
	cursor.
      </description>
      <arg name="x" type="int"/>
      <arg name="y" type="int"/>
      <arg name="width" type="int"/>
      <arg name="height" type="int"/>
    </request>

    <request name="set_preferred_language">
      <description summary="sets preferred language">
	Sets a specific language. This allows for example a virtual keyboard to
	show a language specific layout. The "language" argument is a RFC-3066
	format language tag.

	It could be used for example in a word processor to indicate language of
	currently edited document or in an instant message application which
	tracks languages of contacts.
      </description>
      <arg name="language" type="string"/>
    </request>

    <enum name="update_state">
      <description summary="update_state flags">
	Defines the reason for sending an updated state.
      </description>
      <entry name="change" value="0" summary="updated state because it changed"/>
      <entry name="full" value="1" summary="full state after enter or input_method_changed event"/>
      <entry name="reset" value="2" summary="full state after reset"/>
      <entry name="enter" value="3" summary="full state after switching focus to a different widget on client side"/>
    </enum>

    <request name="update_state">
      <description summary="update state">
	Allows to atomically send state updates from client.

	This request should follow after a batch of state updating requests
	like set_surrounding_text, set_content_type, set_cursor_rectangle and
	set_preferred_language.

	The flags field indicates why an updated state is sent to the input
	method.

	Reset should be used by an editor widget after the text was changed
	outside of the normal input method flow.

	For "change" it is enough to send the changed state, else the full
	state should be send.

	Serial should be set to the serial from the last enter or
	input_method_changed event.

	To make sure to not receive outdated input method events after a
	reset or switching to a new widget wl_display_sync() should be used
	after update_state in these cases.
      </description>
      <arg name="serial" type="uint" summary="serial of the enter or input_method_changed event"/>
      <arg name="reason" type="uint" enum="update_state"/>
    </request>

    <event name="enter">
      <description summary="enter event">
	Notification that this seat's text-input focus is on a certain surface.

	When the seat has the keyboard capability the text-input focus follows
	the keyboard focus.
      </description>
      <arg name="serial" type="uint"  summary="serial to be used by update_state"/>
      <arg name="surface" type="object" interface="wl_surface"/>
    </event>

    <event name="leave">
      <description summary="leave event">
	Notification that this seat's text-input focus is no longer on
	a certain surface.

	The leave notification is sent before the enter notification
	for the new focus.

	When the seat has the keyboard capabillity the text-input focus follows
	the keyboard focus.
      </description>
      <arg name="serial" type="uint"/>
      <arg name="surface" type="object" interface="wl_surface"/>
    </event>

    <enum name="input_panel_visibility">
    <entry name="hidden" value="0"
	   summary="the input panel (virtual keyboard) is hidden"/>
    <entry name="visible" value="1"
	   summary="the input panel (virtual keyboard) is visible"/>
    </enum>

    <event name="input_panel_state">
      <description summary="state of the input panel">
	Notification that the visibility of the input panel (virtual keyboard)
	changed.

	The rectangle x, y, width, height defines the area overlapped by the
	input panel (virtual keyboard) on the surface having the text
	focus in surface local coordinates.

	That can be used to make sure widgets are visible and not covered by
	a virtual keyboard.
      </description>
      <arg name="state" type="uint" enum="input_panel_visibility"/>
      <arg name="x" type="int"/>
      <arg name="y" type="int"/>
      <arg name="width" type="int"/>
      <arg name="height" type="int"/>
    </event>

    <event name="preedit_string">
      <description summary="pre-edit">
	Notify when a new composing text (pre-edit) should be set around the
	current cursor position. Any previously set composing text should
	be removed.

	The commit text can be used to replace the composing text in some cases
	(for example when losing focus).

	The text input should also handle all preedit_style and preedit_cursor
	events occurring directly before preedit_string.
      </description>
      <arg name="text" type="string"/>
      <arg name="commit" type="string"/>
    </event>

    <enum name="preedit_style">
      <entry name="default" value="0" summary="default style for composing text"/>
      <entry name="none" value="1" summary="composing text should be shown the same as non-composing text"/>
      <entry name="active" value="2" summary="composing text might be bold"/>
      <entry name="inactive" value="3" summary="composing text might be cursive"/>
      <entry name="highlight" value="4" summary="composing text might have a different background color"/>
      <entry name="underline" value="5" summary="composing text might be underlined"/>
      <entry name="selection" value="6" summary="composing text should be shown the same as selected text"/>
      <entry name="incorrect" value="7" summary="composing text might be underlined with a red wavy line"/>
    </enum>

    <event name="preedit_styling">
      <description summary="pre-edit styling">
	Sets styling information on composing text. The style is applied for
	length bytes from index relative to the beginning of the composing
	text (as byte offset). Multiple styles can be applied to a composing
	text by sending multiple preedit_styling events.

	This event is handled as part of a following preedit_string event.
      </description>
      <arg name="index" type="uint"/>
      <arg name="length" type="uint"/>
      <arg name="style" type="uint" enum="preedit_style"/>
    </event>

    <event name="preedit_cursor">
      <description summary="pre-edit cursor">
	Sets the cursor position inside the composing text (as byte
	offset) relative to the start of the composing text. When index is a
	negative number no cursor is shown.

	When no preedit_cursor event is sent the cursor will be at the end of
	the composing text by default.

	This event is handled as part of a following preedit_string event.
      </description>
      <arg name="index" type="int"/>
    </event>

    <event name="commit_string">
      <description summary="commit">
	Notify when text should be inserted into the editor widget. The text to
	commit could be either just a single character after a key press or the
	result of some composing (pre-edit). It could be also an empty text
	when some text should be removed (see delete_surrounding_text) or when
	the input cursor should be moved (see cursor_position).

	Any previously set composing text should be removed.
      </description>
      <arg name="text" type="string"/>
    </event>

    <event name="cursor_position">
      <description summary="set cursor to new position">
	Notify when the cursor or anchor position should be modified.

	This event should be handled as part of a following commit_string
	event.

	The text between anchor and index should be selected.
      </description>
      <arg name="index" type="int" summary="position of cursor"/>
      <arg name="anchor" type="int" summary="position of selection anchor"/>
    </event>

    <event name="delete_surrounding_text">
      <description summary="delete surrounding text">
	Notify when the text around the current cursor position should be
	deleted. BeforeLength and afterLength is the length (in bytes) of text
	before and after the current cursor position (excluding the selection)
	to delete.

	This event should be handled as part of a following commit_string
	or preedit_string event.
      </description>
      <arg name="before_length" type="uint" summary="length of text before current cursor positon"/>
      <arg name="after_length" type="uint" summary="length of text after current cursor positon"/>
    </event>

    <event name="modifiers_map">
      <description summary="modifiers map">
	Transfer an array of 0-terminated modifiers names. The position in
	the array is the index of the modifier as used in the modifiers
	bitmask in the keysym event.
      </description>
      <arg name="map" type="array"/>
    </event>

    <event name="keysym">
      <description summary="keysym">
	Notify when a key event was sent. Key events should not be used
	for normal text input operations, which should be done with
	commit_string, delete_surrounding_text, etc. The key event follows
	the wl_keyboard key event convention. Sym is a XKB keysym, state a
	wl_keyboard key_state. Modifiers are a mask for effective modifiers
	(where the modifier indices are set by the modifiers_map event)
      </description>
      <arg name="time" type="uint"/>
      <arg name="sym" type="uint"/>
      <arg name="state" type="uint"/>
      <arg name="modifiers" type="uint"/>
    </event>

    <event name="language">
      <description summary="language">
	Sets the language of the input text. The "language" argument is a RFC-3066
	format language tag.
      </description>
      <arg name="language" type="string"/>
    </event>

    <enum name="text_direction">
      <entry name="auto" value="0" summary="automatic text direction based on text and language"/>
      <entry name="ltr" value="1" summary="left-to-right"/>
      <entry name="rtl" value="2" summary="right-to-left"/>
    </enum>

    <event name="text_direction">
      <description summary="text direction">
	Sets the text direction of input text.

	It is mainly needed for showing input cursor on correct side of the
	editor when there is no input yet done and making sure neutral
	direction text is laid out properly.
      </description>
      <arg name="direction" type="uint" enum="text_direction"/>
    </event>

    <event name="configure_surrounding_text">
      <description summary="configure amount of surrounding text to be sent">
	Configure what amount of surrounding text is expected by the
	input method. The surrounding text will be sent in the
	set_surrounding_text request on the following state information updates.
      </description>
      <arg name="before_cursor" type="int"/>
      <arg name="after_cursor" type="int"/>
    </event>

    <event name="input_method_changed">
      <description summary="Notifies about a changed input method">
	The input method changed on compositor side, which invalidates all
	current state information. New state information should be sent from
	the client via state requests (set_surrounding_text,
	set_content_hint, ...) and update_state.
      </description>
      <arg name="serial" type="uint" summary="serial to be used by update_state"/>
      <arg name="flags" type="uint" summary="currently unused"/>
    </event>
  </interface>

  <interface name="zwp_text_input_manager_v2" version="1">
    <description summary="text input manager">
      A factory for text-input objects. This object is a global singleton.
    </description>

    <request name="destroy" type="destructor">
      <description summary="Destroy the wp_text_input_manager">
	Destroy the wp_text_input_manager object.
      </description>
    </request>

    <request name="get_text_input">
      <description summary="create a new text input object">
	Creates a new text-input object for a given seat.
      </description>
      <arg name="id" type="new_id" interface="zwp_text_input_v2"/>
      <arg name="seat" type="object" interface="wl_seat"/>
    </request>
  </interface>
</protocol>
