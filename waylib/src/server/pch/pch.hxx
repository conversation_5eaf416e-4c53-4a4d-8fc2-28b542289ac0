#ifdef __cplusplus
#include<qwallocator.h>
#include<qwbackend.h>
#include<qwbackendinterface.h>
#include<qwbox.h>
#include<qwbuffer.h>
#include<qwbufferinterface.h>
#include<qwcompositor.h>
#include<qwconfig.h>
#include<qwcontenttypev1.h>
#include<qwcursor.h>
#include<qwcursorshapev1.h>
#include<qwdamagering.h>
#include<qwdatacontrolv1.h>
#include<qwdatadevice.h>
#include<qwdisplay.h>
#include<qwdrm.h>
#include<qwdrmleasev1.h>
#include<qwegl.h>
#include<qwexportdmabufv1.h>
#include<qwforeigntoplevelhandlev1.h>
#include<qwfractionalscalemanagerv1.h>
#include<qwfullscreenshellv1.h>
#include<qwgammacontorlv1.h>
#include<qwglobal.h>
#include<qwidleinhibitv1.h>
#include<qwidlenotifyv1.h>
#include<qwinputdevice.h>
#include<qwinputmethodv2.h>
#include<qwinterface.h>
#include<qwkeyboardgroup.h>
#include<qwkeyboard.h>
#include<qwkeyboardinterface.h>
#include<qwkeyboardshortcutsinhibitv1.h>
#include<qwlayershellv1.h>
#include<qwlinuxdmabufv1.h>
#include<qwlogging.h>
#include<qwobject.h>
#include<qwoutput.h>
#include<qwoutputinterface.h>
#include<qwoutputlayer.h>
#include<qwoutputlayout.h>
#include<qwoutputmanagementv1.h>
#include<qwoutputpowermanagementv1.h>
#include<qwpointerconstraintsv1.h>
#include<qwpointergesturesv1.h>
#include<qwpointer.h>
#include<qwpointerinterface.h>
#include<qwpresentation.h>
#include<qwprimaryselection.h>
#include<qwprimaryselectionv1.h>
#include<qwrelativepointerv1.h>
#include<qwrenderer.h>
#include<qwrendererinterface.h>
#include<qwscene.h>
#include<qwscreencopyv1.h>
#include<qwseat.h>
#include<qwsecuritycontextmanagerv1.h>
#include<qwsessionlockv1.h>
#include<qwshm.h>
#include<qwsignalconnector.h>
#include<qwsinglepixelbufferv1.h>
#include<qwsubcompositor.h>
#include<qwswapchain.h>
#include<qwswitch.h>
#include<qwswitchinterface.h>
#include<qwtablet.h>
#include<qwtabletpad.h>
#include<qwtabletpadinterface.h>
#include<qwtabletv2.h>
#include<qwtearingcontrolv1.h>
#include<qwtextinputv3.h>
#include<qwtexture.h>
#include<qwtouch.h>
#include<qwviewporter.h>
#include<qwvirtualkeyboardv1.h>
#include<qwvirtualpointerv1.h>
#include<qwxcursormanager.h>
#include<qwxdgactivationv1.h>
#include<qwxdgdecorationmanagerv1.h>
#include<qwxdgforeignregistry.h>
#include<qwxdgforeignv1.h>
#include<qwxdgforeignv2.h>
#include<qwxdgoutputv1.h>
#include<qwxdgshell.h>
#include<qwxwayland.h>
#include<qwxwaylandserver.h>
#include<qwxwaylandshellv1.h>
#include<qwxwaylandsurface.h>
#include<QQmlEngine>
#include<QGuiApplication>
#endif // __cplusplus
