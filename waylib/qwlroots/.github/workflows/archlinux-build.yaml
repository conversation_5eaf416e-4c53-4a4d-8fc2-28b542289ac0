name: Build on archlinux

on:
  push:
    branches:
      - master

  pull_request:
    branches:
      - master

jobs:
  container:
    runs-on: ubuntu-latest
    container: archlinux:latest
    steps:
      - uses: actions/checkout@v4
      - name: Run in container
        run: |
          pacman-key --init
          pacman -Syu --noconfirm
      - name: Install dep
        run: |
          pacman -Syu --noconfirm qt6-base cmake pkgconfig wlroots pixman wayland-protocols wlr-protocols clazy
          pacman -Syu --noconfirm clang ninja
      - name: Configure CMake
        run: |
          # export PKG_CONFIG_PATH=/usr/lib/pkgconfig/:/usr/lib/wlroots0.17/pkgconfig/
          mkdir -p ${{github.workspace}}/build
          cmake -B ${{github.workspace}}/build -G Ninja -DCMAKE_CXX_COMPILER=clazy
      - name: Build
        # Build your program with the given configuration
        run: cmake --build ${{github.workspace}}/build
