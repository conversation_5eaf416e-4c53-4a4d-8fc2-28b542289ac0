Format: https://www.debian.org/doc/packaging-manuals/copyright-format/1.0/
Upstream-Name: qwlroots
Upstream-Contact: <PERSON><PERSON><PERSON> <<EMAIL>>
Source: https://github.com/vioken/qwlroots

# ci
Files: .github/* .obs/*.yml garnix.yaml
Copyright: None
License: CC0-1.0

# Project file
Files: *.cmake *CMakeLists.txt *.pc.in *cmake.in .gitignore
Copyright: None
License: CC0-1.0

# Simple header files, only used to include qwinputdevice.h
Files: */qwkeyboard.h */qwpointer.h */qwswitch.h */qwtablet.h */qwtabletpad.h */qwtouch.h
Copyright: None
License: CC0-1.0

# README
Files: README.md README.zh_CN.md
Copyright: JiDe Zhang
License: CC-BY-4.0

# Nix Develop files
Files: *.nix .envrc flake.lock
Copyright: None
License: CC0-1.0

# deb package
Files: debian/*
Copyright: 2023 rewine <<EMAIL>>
License: Apache-2.0 OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
