// Copyright (C) 2023 JiD<PERSON> <<EMAIL>>.
// SPDX-License-Identifier: Apache-2.0 OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only

#cmakedefine WLR_HAVE_DRM_BACKEND @WLR_HAVE_DRM_BACKEND@
#cmakedefine WLR_HAVE_X11_BACKEND @WLR_HAVE_X11_BACKEND@
#cmakedefine WLR_HAVE_LIBINPUT_BACKEND @WLR_HAVE_LIBINPUT_BACKEND@
#cmakedefine WLR_HAVE_XWAYLAND @WLR_HAVE_XWAYLAND@
#cmakedefine WLR_HAVE_GLES2_RENDERER @WLR_HAVE_GLES2_RENDERER@
#cmakedefine WLR_HAVE_VULKAN_RENDERER @WLR_HAVE_VULKAN_RENDERER@
#cmakedefine WLR_HAVE_GBM_ALLOCATOR @WLR_HAVE_GBM_ALLOCATOR@
#cmakedefine WLR_HAVE_SESSION @WLR_HAVE_SESSION@

#define WLR_VERSION "@WLROOTS_VERSION@"
#define WLR_VERSION_MAJOR @WLROOTS_VERSION_MAJOR@
#define WLR_VERSION_MINOR @WLROOTS_VERSION_MINOR@
#define WLR_VERSION_PATCH @WLROOTS_VERSION_PATCH@
