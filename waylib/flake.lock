{"nodes": {"flake-utils": {"inputs": {"systems": "systems"}, "locked": {"lastModified": 1731533236, "narHash": "sha256-l0KFg5HjrsfsO/JpG+r7fRrqm12kzFHyUHqHCVpMMbI=", "owner": "numtide", "repo": "flake-utils", "rev": "11707dc2f618dd54ca8739b309ec4fc024de578b", "type": "github"}, "original": {"owner": "numtide", "repo": "flake-utils", "type": "github"}}, "nix-filter": {"locked": {"lastModified": 1731533336, "narHash": "sha256-oRam5PS1vcrr5UPgALW0eo1m/5/pls27Z/pabHNy2Ms=", "owner": "numtide", "repo": "nix-filter", "rev": "f7653272fd234696ae94229839a99b73c9ab7de0", "type": "github"}, "original": {"owner": "numtide", "repo": "nix-filter", "type": "github"}}, "nixpkgs": {"locked": {"lastModified": 1739866667, "narHash": "sha256-EO1ygNKZlsAC9avfcwHkKGMsmipUk1Uc0TbrEZpkn64=", "owner": "NixOS", "repo": "nixpkgs", "rev": "73cf49b8ad837ade2de76f87eb53fc85ed5d4680", "type": "github"}, "original": {"owner": "NixOS", "ref": "nixos-unstable", "repo": "nixpkgs", "type": "github"}}, "qwlroots": {"inputs": {"flake-utils": ["flake-utils"], "nix-filter": ["nix-filter"], "nixpkgs": ["nixpkgs"]}, "locked": {"lastModified": 1740050711, "narHash": "sha256-ZyG0JGUlz/ubtwN5wYtC8qeYsPur+0kTkD7iIjHX7KU=", "owner": "vioken", "repo": "qwlroots", "rev": "152e27cc65e02ed9000219dbf3cd31efbb1bab11", "type": "github"}, "original": {"owner": "vioken", "repo": "qwlroots", "type": "github"}}, "root": {"inputs": {"flake-utils": "flake-utils", "nix-filter": "nix-filter", "nixpkgs": "nixpkgs", "qwlroots": "qwlroots"}}, "systems": {"locked": {"lastModified": 1681028828, "narHash": "sha256-Vy1rq5AaRuLzOxct8nz4T6wlgyUR7zLU309k9mBC768=", "owner": "nix-systems", "repo": "default", "rev": "da67096a3b9bf56a91d16901293e51ba5b49a27e", "type": "github"}, "original": {"owner": "nix-systems", "repo": "default", "type": "github"}}}, "root": "root", "version": 7}