{"magic": "dsg.config.meta", "version": "1.0", "contents": {"showOnLock": {"value": false, "serial": 0, "flags": ["user-public"], "name": "Show on lock screen", "name[zh_CN]": "显示在锁屏之上", "description": "Allow show context on lock screen", "description[zh_CN]": "允许在锁屏界面上显示内容", "permissions": "readwrite", "visibility": "public"}, "lostScreen": {"value": "MoveToPrimary", "serial": 0, "flags": ["user-public"], "name": "Behaivor when lost screen", "name[zh_CN]": "丢失屏幕的行为", "description": "Behaivor when lost screen, allow value: MoveToPrimary、Minimize", "description[zh_CN]": "丢失屏幕的行为，可选值为：MoveToPrimary、Minimize", "permissions": "readwrite", "visibility": "public"}, "font": {"value": "Source Han Sans SC", "serial": 0, "flags": ["user-public"], "name": "Font", "name[zh_CN]": "字体", "description": "Font Family", "description[zh_CN]": "字体", "permissions": "readwrite", "visibility": "public"}, "monoFont": {"value": "Noto Mono", "serial": 0, "flags": ["user-public"], "name": "Mono Font", "name[zh_CN]": "等宽字体", "description": "Mono Font Family", "description[zh_CN]": "等宽字体", "permissions": "readwrite", "visibility": "public"}, "fontSize": {"value": 105, "serial": 0, "flags": ["user-public"], "name": "Font Size", "name[zh_CN]": "字体大小", "description": "Font Size", "description[zh_CN]": "字体大小", "permissions": "readwrite", "visibility": "public"}, "windowRadius": {"value": 12, "serial": 0, "flags": ["user-public"], "name": "Round Corner Radius", "name[zh_CN]": "窗口圆角大小", "description": "Window Corner Round Radius", "description[zh_CN]": "设置窗口圆角大小", "permissions": "readwrite", "visibility": "public"}, "preferDark": {"value": false, "serial": 0, "flags": ["user-public"], "name": "Prefer Dark", "name[zh_CN]": "倾向暗色", "description": "Color scheme is prefer dark", "description[zh_CN]": "颜色方案是否倾向暗色", "permissions": "readwrite", "visibility": "public"}, "themeName": {"value": "", "serial": 0, "flags": ["user-public"], "name": "Theme Name", "name[zh_CN]": "主题名称", "description": "Theme Name", "description[zh_CN]": "主题名称", "permissions": "readwrite", "visibility": "public"}, "iconThemeName": {"value": "nirvana", "serial": 0, "flags": ["user-public"], "name": "Icon Theme Name", "name[zh_CN]": "图标主题名称", "description": "Icon Theme Name", "description[zh_CN]": "图标主题名称", "permissions": "readwrite", "visibility": "public"}, "cursorThemeName": {"value": "bloom", "serial": 0, "flags": ["user-public"], "name": "Cursor Theme Name", "name[zh_CN]": "光标主题名称", "description": "Cursor Theme Name", "description[zh_CN]": "光标主题名称", "permissions": "readwrite", "visibility": "public"}, "cursorSize": {"value": 24, "serial": 0, "flags": ["user-public"], "name": "<PERSON><PERSON><PERSON>", "name[zh_CN]": "光标大小", "description": "<PERSON><PERSON><PERSON>", "description[zh_CN]": "光标大小", "permissions": "readwrite", "visibility": "public"}, "cursorBlink": {"value": true, "serial": 0, "flags": ["user-public"], "name": "Cursor Blink", "name[zh_CN]": "光标闪烁", "description": "Whether the cursor should blink", "description[zh_CN]": "光标是否应闪烁", "permissions": "readwrite", "visibility": "public"}, "cursorBlinkTime": {"value": 1200, "serial": 0, "flags": ["user-public"], "name": "Cursor Blink Time", "name[zh_CN]": "光标闪烁时间", "description": "Length of the cursor blink cycle, in milleseconds", "description[zh_CN]": "光标闪烁周期的长度，以千秒为单位", "permissions": "readwrite", "visibility": "public"}, "doubleClickTime": {"value": 250, "serial": 0, "flags": ["user-public"], "name": "Double Click Time", "name[zh_CN]": "双击时间", "description": "Maximum time allowed between two clicks for them to be considered a double click (in milliseconds)", "description[zh_CN]": "两次单击之间允许被视为双击的最长时间（以毫秒为单位）", "permissions": "readwrite", "visibility": "public"}, "doubleClickDistance": {"value": 5, "serial": 0, "flags": ["user-public"], "name": "Double Click Distance", "name[zh_CN]": "双击距离", "description": "Maximum distance allowed between two clicks for them to be considered a double click (in pixels)", "description[zh_CN]": "两次单击之间允许被视为双击的最大距离（以像素为单位）", "permissions": "readwrite", "visibility": "public"}, "dndDragThreshold": {"value": 8, "serial": 0, "flags": ["user-public"], "name": "Dnd Drag Threshold", "name[zh_CN]": "拖动阈值", "description": "Number of pixels the cursor can move before dragging", "description[zh_CN]": "拖动之前光标可以移动的像素数", "permissions": "readwrite", "visibility": "public"}, "maxWorkspace": {"value": 6, "serial": 0, "flags": ["user-public"], "name": "Max Workspace Count", "name[zh_CN]": "工作区最大数量", "description": "Maximum number of workspaces", "description[zh_CN]": "工作区最大数量", "permissions": "readwrite", "visibility": "private"}, "numWorkspace": {"value": 2, "serial": 0, "flags": ["user-public"], "name": "Number of workspaces", "name[zh_CN]": "工作区数量", "description": "Current number of workspaces", "description[zh_CN]": "当前工作区数量", "permissions": "readwrite", "visibility": "private"}, "currentWorkspace": {"value": 0, "serial": 0, "flags": ["user-public"], "name": "Current workspace", "name[zh_CN]": "当前工作区", "description": "Current index of workspace", "description[zh_CN]": "当前工作区索引", "permissions": "readwrite", "visibility": "private"}, "forceSoftwareCursor": {"value": false, "serial": 0, "flags": ["global"], "name": "Force Software Cursor", "name[zh_CN]": "强制软光标渲染", "description": "Force software cursor rendering", "description[zh_CN]": "强制使用软光标进行渲染", "permissions": "readwrite", "visibility": "public"}, "activeColor": {"value": "#1F6EE7", "serial": 0, "flags": ["global"], "name": "Global Window Active Color", "name[zh_CN]": "全局窗口活动色", "description": "Set window active color", "description[zh_CN]": "设置窗口活动色", "permissions": "readwrite", "visibility": "public"}, "windowOpacity": {"value": 40, "serial": 0, "flags": ["global"], "name": "Global Window Opacity", "name[zh_CN]": "全局窗口不透明度", "description": "Set all window opacity", "description[zh_CN]": "设置所有窗口的不透明度", "permissions": "readwrite", "visibility": "public"}, "windowThemeType": {"value": 0, "serial": 0, "flags": ["global"], "name": "Global Window Theme Type", "name[zh_CN]": "全局窗口主题类型", "description": "Set all window theme type", "description[zh_CN]": "设置所有窗口的主题类型", "permissions": "readwrite", "visibility": "public"}, "windowTitlebarHeight": {"value": 40, "serial": 0, "flags": ["global"], "name": "Global Window titlebar height", "name[zh_CN]": "全局标题栏高度", "description": "Set all window default titlebar height", "description[zh_CN]": "设置所有窗口默认的标题栏高度", "permissions": "readwrite", "visibility": "public"}, "defaultBackground": {"value": "/usr/share/backgrounds/default_background.jpg", "serial": 0, "flags": ["global"], "name": "Default background", "name[zh_CN]": "默认壁纸", "description": "Set the default displayed wallpaper, when no wallpaper is set", "description[zh_CN]": "当没有设置壁纸时，设置默认显示的壁纸", "permissions": "readwrite", "visibility": "public"}}}