<?xml version="1.0"?>
<!DOCTYPE busconfig PUBLIC "-//freedesktop//DTD D-BUS Bus Configuration 1.0//EN"
        "http://www.freedesktop.org/standards/dbus/1.0/busconfig.dtd">

<busconfig>
        <policy user="dde">
                <allow own="org.deepin.Compositor1"/>
                <allow send_destination="org.deepin.Compositor1"/>
                <allow receive_sender="org.deepin.Compositor1"/>
        </policy>

        <policy context="default">
                <allow send_destination="org.deepin.Compositor1" />
                <allow send_destination="org.deepin.Compositor1"
                        send_interface="org.deepin.Compositor1"/>
                <allow send_destination="org.deepin.Compositor1"
                        send_interface="org.freedesktop.DBus.Properties"/>
                <allow send_destination="org.deepin.Compositor1"
                        send_interface="org.freedesktop.DBus.Introspectable"/>
        </policy>
</busconfig>
