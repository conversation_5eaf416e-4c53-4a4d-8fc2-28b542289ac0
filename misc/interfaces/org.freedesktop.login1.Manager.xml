<!DOCTYPE node PUBLIC "-//freedesktop//DTD D-BUS Object Introspection 1.0//EN"
"http://www.freedesktop.org/standards/dbus/1.0/introspect.dtd">
<node>
 <interface name="org.freedesktop.login1.Manager">
  <property name="NAutoVTs" type="u" access="read">
   <annotation name="org.freedesktop.DBus.Property.EmitsChangedSignal" value="const"/>
  </property>
  <property name="KillOnlyUsers" type="as" access="read">
   <annotation name="org.freedesktop.DBus.Property.EmitsChangedSignal" value="const"/>
  </property>
  <property name="KillExcludeUsers" type="as" access="read">
   <annotation name="org.freedesktop.DBus.Property.EmitsChangedSignal" value="const"/>
  </property>
  <property name="KillUserProcesses" type="b" access="read">
   <annotation name="org.freedesktop.DBus.Property.EmitsChangedSignal" value="const"/>
  </property>
  <property name="IdleHint" type="b" access="read">
  </property>
  <property name="IdleSinceHint" type="t" access="read">
  </property>
  <property name="IdleSinceHintMonotonic" type="t" access="read">
  </property>
  <property name="BlockInhibited" type="s" access="read">
  </property>
  <property name="DelayInhibited" type="s" access="read">
  </property>
  <property name="InhibitDelayMaxUSec" type="t" access="read">
   <annotation name="org.freedesktop.DBus.Property.EmitsChangedSignal" value="const"/>
  </property>
  <property name="HandlePowerKey" type="s" access="read">
   <annotation name="org.freedesktop.DBus.Property.EmitsChangedSignal" value="const"/>
  </property>
  <property name="HandleSuspendKey" type="s" access="read">
   <annotation name="org.freedesktop.DBus.Property.EmitsChangedSignal" value="const"/>
  </property>
  <property name="HandleHibernateKey" type="s" access="read">
   <annotation name="org.freedesktop.DBus.Property.EmitsChangedSignal" value="const"/>
  </property>
  <property name="HandleLidSwitch" type="s" access="read">
   <annotation name="org.freedesktop.DBus.Property.EmitsChangedSignal" value="const"/>
  </property>
  <property name="IdleAction" type="s" access="read">
   <annotation name="org.freedesktop.DBus.Property.EmitsChangedSignal" value="const"/>
  </property>
  <property name="IdleActionUSec" type="t" access="read">
   <annotation name="org.freedesktop.DBus.Property.EmitsChangedSignal" value="const"/>
  </property>
  <property name="PreparingForShutdown" type="b" access="read">
   <annotation name="org.freedesktop.DBus.Property.EmitsChangedSignal" value="false"/>
  </property>
  <property name="PreparingForSleep" type="b" access="read">
   <annotation name="org.freedesktop.DBus.Property.EmitsChangedSignal" value="false"/>
  </property>
  <method name="GetSession">
   <arg type="s" direction="in"/>
   <arg type="o" direction="out"/>
  </method>
  <method name="GetSessionByPID">
   <arg type="u" direction="in"/>
   <arg type="o" direction="out"/>
  </method>
  <method name="GetUser">
   <arg type="u" direction="in"/>
   <arg type="o" direction="out"/>
  </method>
  <method name="GetUserByPID">
   <arg type="u" direction="in"/>
   <arg type="o" direction="out"/>
  </method>
  <method name="GetSeat">
   <arg type="s" direction="in"/>
   <arg type="o" direction="out"/>
  </method>
  <method name="ListSessions">
   <arg type="a(susso)" direction="out"/>
   <annotation name="org.qtproject.QtDBus.QtTypeName.Out0" value="SessionInfoList"/>
  </method>
  <method name="ListUsers">
   <arg type="a(uso)" direction="out"/>
   <annotation name="org.qtproject.QtDBus.QtTypeName.Out0" value="UserInfoList"/>
  </method>
  <method name="ListSeats">
   <arg type="a(so)" direction="out"/>
   <annotation name="org.qtproject.QtDBus.QtTypeName.Out0" value="NamedSeatPathList"/>
  </method>
  <method name="ListInhibitors">
   <arg type="a(ssssuu)" direction="out"/>
   <annotation name="org.qtproject.QtDBus.QtTypeName.Out0" value="InhibitorList"/>
  </method>
  <method name="ReleaseSession">
   <arg type="s" direction="in"/>
   <annotation name="org.freedesktop.systemd1.Privileged" value="true"/>
  </method>
  <method name="ActivateSession">
   <arg type="s" direction="in"/>
  </method>
  <method name="ActivateSessionOnSeat">
   <arg type="s" direction="in"/>
   <arg type="s" direction="in"/>
  </method>
  <method name="LockSession">
   <arg type="s" direction="in"/>
   <annotation name="org.freedesktop.systemd1.Privileged" value="true"/>
  </method>
  <method name="UnlockSession">
   <arg type="s" direction="in"/>
   <annotation name="org.freedesktop.systemd1.Privileged" value="true"/>
  </method>
  <method name="LockSessions">
   <annotation name="org.freedesktop.systemd1.Privileged" value="true"/>
  </method>
  <method name="UnlockSessions">
   <annotation name="org.freedesktop.systemd1.Privileged" value="true"/>
  </method>
  <method name="KillSession">
   <arg type="s" direction="in"/>
   <arg type="s" direction="in"/>
   <arg type="i" direction="in"/>
   <annotation name="org.freedesktop.systemd1.Privileged" value="true"/>
  </method>
  <method name="KillUser">
   <arg type="u" direction="in"/>
   <arg type="i" direction="in"/>
   <annotation name="org.freedesktop.systemd1.Privileged" value="true"/>
  </method>
  <method name="TerminateSession">
   <arg type="s" direction="in"/>
   <annotation name="org.freedesktop.systemd1.Privileged" value="true"/>
  </method>
  <method name="TerminateUser">
   <arg type="u" direction="in"/>
   <annotation name="org.freedesktop.systemd1.Privileged" value="true"/>
  </method>
  <method name="TerminateSeat">
   <arg type="s" direction="in"/>
   <annotation name="org.freedesktop.systemd1.Privileged" value="true"/>
  </method>
  <method name="SetUserLinger">
   <arg type="u" direction="in"/>
   <arg type="b" direction="in"/>
   <arg type="b" direction="in"/>
  </method>
  <method name="AttachDevice">
   <arg type="s" direction="in"/>
   <arg type="s" direction="in"/>
   <arg type="b" direction="in"/>
  </method>
  <method name="FlushDevices">
   <arg type="b" direction="in"/>
  </method>
  <method name="PowerOff">
   <arg type="b" direction="in"/>
  </method>
  <method name="Reboot">
   <arg type="b" direction="in"/>
  </method>
  <method name="Suspend">
   <arg type="b" direction="in"/>
  </method>
  <method name="Hibernate">
   <arg type="b" direction="in"/>
  </method>
  <method name="HybridSleep">
   <arg type="b" direction="in"/>
  </method>
  <method name="CanPowerOff">
   <arg type="s" direction="out"/>
  </method>
  <method name="CanReboot">
   <arg type="s" direction="out"/>
  </method>
  <method name="CanSuspend">
   <arg type="s" direction="out"/>
  </method>
  <method name="CanHibernate">
   <arg type="s" direction="out"/>
  </method>
  <method name="CanHybridSleep">
   <arg type="s" direction="out"/>
  </method>
  <method name="Inhibit">
   <arg type="s" direction="in"/>
   <arg type="s" direction="in"/>
   <arg type="s" direction="in"/>
   <arg type="s" direction="in"/>
   <arg type="h" direction="out"/>
  </method>
  <signal name="SessionNew">
   <arg type="s"/>
   <arg type="o"/>
  </signal>
  <signal name="SessionRemoved">
   <arg type="s"/>
   <arg type="o"/>
  </signal>
  <signal name="UserNew">
   <arg type="u"/>
   <arg type="o"/>
  </signal>
  <signal name="UserRemoved">
   <arg type="u"/>
   <arg type="o"/>
  </signal>
  <signal name="SeatNew">
   <arg type="s"/>
   <arg type="o"/>
  </signal>
  <signal name="SeatRemoved">
   <arg type="s"/>
   <arg type="o"/>
  </signal>
  <signal name="PrepareForShutdown">
   <arg type="b"/>
  </signal>
  <signal name="PrepareForSleep">
   <arg type="b"/>
  </signal>
 </interface>
</node>

