<!DOCTYPE node PUBLIC "-//freedesktop//DTD D-BUS Object Introspection
1.0//EN" "http://www.freedesktop.org/standards/dbus/1.0/introspect.dtd">
<node>
    <interface name="org.freedesktop.DisplayManager">
        <!--
        <method name="AddSeat">
            <arg type="s" name="type" direction="in">
            </arg>
            <arg type="a(ss)" name="properties" direction="in">
            </arg>
            <arg type="o" name="seat" direction="out">
            </arg>
        </method>
        <method name="AddLocalXSeat">
            <arg type="i" name="display_number" direction="in">
            </arg>
            <arg type="o" name="seat" direction="out">
            </arg>
        </method>
	//-->
        <signal name="SeatAdded">
            <arg type="o" name="seat">
            </arg>
        </signal>
        <signal name="SeatRemoved">
            <arg type="o" name="seat">
            </arg>
        </signal>
        <signal name="SessionAdded">
            <arg type="o" name="session">
            </arg>
        </signal>
        <signal name="SessionRemoved">
            <arg type="o" name="session">
            </arg>
        </signal>
        <property type="ao" name="Seats" access="read">
        </property>
        <property type="ao" name="Sessions" access="read">
        </property>
        <property type="o" name="LastSession" access="read">
        </property>
    </interface>
</node>
