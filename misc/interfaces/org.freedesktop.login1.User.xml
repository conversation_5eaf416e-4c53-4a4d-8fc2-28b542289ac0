<!DOCTYPE node PUBLIC "-//freedesktop//DTD D-BUS Object Introspection 1.0//EN"
"http://www.freedesktop.org/standards/dbus/1.0/introspect.dtd">
<node>
 <interface name="org.freedesktop.login1.User">
  <property name="UID" type="u" access="read">
   <annotation name="org.freedesktop.DBus.Property.EmitsChangedSignal" value="const"/>
  </property>
  <property name="GID" type="u" access="read">
   <annotation name="org.freedesktop.DBus.Property.EmitsChangedSignal" value="const"/>
  </property>
  <property name="Name" type="s" access="read">
   <annotation name="org.freedesktop.DBus.Property.EmitsChangedSignal" value="const"/>
  </property>
  <property name="Timestamp" type="t" access="read">
   <annotation name="org.freedesktop.DBus.Property.EmitsChangedSignal" value="const"/>
  </property>
  <property name="TimestampMonotonic" type="t" access="read">
   <annotation name="org.freedesktop.DBus.Property.EmitsChangedSignal" value="const"/>
  </property>
  <property name="RuntimePath" type="s" access="read">
   <annotation name="org.freedesktop.DBus.Property.EmitsChangedSignal" value="const"/>
  </property>
  <property name="Service" type="s" access="read">
   <annotation name="org.freedesktop.DBus.Property.EmitsChangedSignal" value="const"/>
  </property>
  <property name="Slice" type="s" access="read">
   <annotation name="org.freedesktop.DBus.Property.EmitsChangedSignal" value="const"/>
  </property>
  <property name="Display" type="(so)" access="read">
   <annotation name="org.qtproject.QtDBus.QtTypeName" value="NamedSessionPath"/>
  </property>
  <property name="State" type="s" access="read">
   <annotation name="org.freedesktop.DBus.Property.EmitsChangedSignal" value="false"/>
  </property>
  <property name="Sessions" type="a(so)" access="read">
   <annotation name="org.qtproject.QtDBus.QtTypeName" value="QList&lt;NamedSessionPath&gt;"/>
  </property>
  <property name="IdleHint" type="b" access="read">
  </property>
  <property name="IdleSinceHint" type="t" access="read">
  </property>
  <property name="IdleSinceHintMonotonic" type="t" access="read">
  </property>
  <property name="Linger" type="b" access="read">
   <annotation name="org.freedesktop.DBus.Property.EmitsChangedSignal" value="false"/>
  </property>
  <method name="Terminate">
   <annotation name="org.freedesktop.systemd1.Privileged" value="true"/>
  </method>
  <method name="Kill">
   <arg type="i" direction="in"/>
   <annotation name="org.freedesktop.systemd1.Privileged" value="true"/>
  </method>
 </interface>
</node>

