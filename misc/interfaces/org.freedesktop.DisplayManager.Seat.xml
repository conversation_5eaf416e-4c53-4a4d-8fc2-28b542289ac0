<!DOCTYPE node PUBLIC "-//freedesktop//DTD D-BUS Object Introspection 1.0//EN" "http://www.freedesktop.org/standards/dbus/1.0/introspect.dtd">
<node>
    <interface name="org.freedesktop.DisplayManager.Seat">
        <method name="SwitchToGreeter">
        </method>
        <method name="SwitchToUser">
            <arg type="s" name="username" direction="in">
            </arg>
            <arg type="s" name="session_name" direction="in">
            </arg>
        </method>
        <method name="SwitchToGuest">
            <arg type="s" name="session_name" direction="in">
            </arg>
        </method>
        <method name="Lock">
        </method>
        <property type="b" name="CanSwitch" access="read">
        </property>
        <property type="b" name="HasGuestAccount" access="read">
        </property>
        <property type="ao" name="Sessions" access="read">
        </property>
    </interface>
</node>
