[Unit]
Description=Treeland shortcut service
CollectMode=inactive-or-failed

Requires=treeland-sd.service
PartOf=treeland-sd.service
After=treeland-sd.service

Requisite=dde-session-pre.target
PartOf=dde-session-pre.target
Before=dde-session-pre.target

[Service]
ExecCondition=/bin/sh -c 'test "$XDG_SESSION_DESKTOP" = "Treeland" || exit 2'
Type=notify
ExecStart=@CMAKE_INSTALL_FULL_LIBEXECDIR@/treeland-shortcut
Slice=session.slice
RestartSec=3s
Restart=on-failure
