[Unit]
Description=Treeland socket helper service
CollectMode=inactive-or-failed

Requires=treeland.service
PartOf=treeland.service
After=treeland.service

Requires=treeland-sd.socket
PartOf=treeland-sd.socket
After=treeland-sd.socket

Requisite=dde-session-pre.target
PartOf=dde-session-pre.target
Before=dde-session-pre.target

[Service]
ExecCondition=/bin/sh -c 'test "$XDG_SESSION_DESKTOP" = "Treeland" || exit 2'
Type=notify
Sockets=treeland-sd.socket
UnsetEnvironment=DISPLAY
UnsetEnvironment=WAYLAND_DISPLAY
ExecStart=@CMAKE_INSTALL_FULL_LIBEXECDIR@/treeland-sd --type wayland
ExecStartPost=-/usr/bin/systemctl --user set-environment WAYLAND_DISPLAY=%t/treeland.socket
ExecStop=-/usr/bin/systemctl --user unset-environment WAYLAND_DISPLAY
Slice=session.slice
RestartSec=1s
