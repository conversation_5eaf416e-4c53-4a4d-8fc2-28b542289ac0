{"nodes": {"ddm": {"inputs": {"flake-utils": ["flake-utils"], "nix-filter": ["nix-filter"], "nixpkgs": ["nixpkgs"]}, "locked": {"lastModified": 1737432823, "narHash": "sha256-pty5k1MQ9Fh68dscINllJ3Df3mzUgcB0HgBGFvw1Xgs=", "owner": "linuxdeepin", "repo": "ddm", "rev": "4cfcaf4184a47780b248ea9a43e38563ae0d55ab", "type": "github"}, "original": {"owner": "linuxdeepin", "repo": "ddm", "type": "github"}}, "flake-utils": {"inputs": {"systems": "systems"}, "locked": {"lastModified": 1731533236, "narHash": "sha256-l0KFg5HjrsfsO/JpG+r7fRrqm12kzFHyUHqHCVpMMbI=", "owner": "numtide", "repo": "flake-utils", "rev": "11707dc2f618dd54ca8739b309ec4fc024de578b", "type": "github"}, "original": {"owner": "numtide", "repo": "flake-utils", "type": "github"}}, "nix-filter": {"locked": {"lastModified": 1731533336, "narHash": "sha256-oRam5PS1vcrr5UPgALW0eo1m/5/pls27Z/pabHNy2Ms=", "owner": "numtide", "repo": "nix-filter", "rev": "f7653272fd234696ae94229839a99b73c9ab7de0", "type": "github"}, "original": {"owner": "numtide", "repo": "nix-filter", "type": "github"}}, "nixpkgs": {"locked": {"lastModified": 1744463964, "narHash": "sha256-LWqduOgLHCFxiTNYi3Uj5Lgz0SR+Xhw3kr/3Xd0GPTM=", "owner": "NixOS", "repo": "nixpkgs", "rev": "2631b0b7abcea6e640ce31cd78ea58910d31e650", "type": "github"}, "original": {"owner": "NixOS", "ref": "nixos-unstable", "repo": "nixpkgs", "type": "github"}}, "qwlroots": {"inputs": {"flake-utils": ["flake-utils"], "nix-filter": ["nix-filter"], "nixpkgs": ["nixpkgs"]}, "locked": {"lastModified": 1740050711, "narHash": "sha256-ZyG0JGUlz/ubtwN5wYtC8qeYsPur+0kTkD7iIjHX7KU=", "owner": "vioken", "repo": "qwlroots", "rev": "152e27cc65e02ed9000219dbf3cd31efbb1bab11", "type": "github"}, "original": {"owner": "vioken", "repo": "qwlroots", "type": "github"}}, "root": {"inputs": {"ddm": "ddm", "flake-utils": "flake-utils", "nix-filter": "nix-filter", "nixpkgs": "nixpkgs", "qwlroots": "qwlroots", "treeland-protocols": "treeland-protocols", "waylib": "waylib"}}, "systems": {"locked": {"lastModified": 1681028828, "narHash": "sha256-Vy1rq5AaRuLzOxct8nz4T6wlgyUR7zLU309k9mBC768=", "owner": "nix-systems", "repo": "default", "rev": "da67096a3b9bf56a91d16901293e51ba5b49a27e", "type": "github"}, "original": {"owner": "nix-systems", "repo": "default", "type": "github"}}, "treeland-protocols": {"inputs": {"flake-utils": ["flake-utils"], "nix-filter": ["nix-filter"], "nixpkgs": ["nixpkgs"]}, "locked": {"lastModified": 1737101824, "narHash": "sha256-keu/J/mEW7VIQ2Nyo9SaOreUYoDdgxONb66c6XN3Gnc=", "owner": "linuxdeepin", "repo": "treeland-protocols", "rev": "b087c563ad5b07f61ebbe2417251839ef6876d45", "type": "github"}, "original": {"owner": "linuxdeepin", "repo": "treeland-protocols", "type": "github"}}, "waylib": {"inputs": {"flake-utils": ["flake-utils"], "nix-filter": ["nix-filter"], "nixpkgs": ["nixpkgs"], "qwlroots": ["qwlroots"]}, "locked": {"lastModified": 1744423174, "narHash": "sha256-Yp7j1L+b41pmLWhxYN4M9W8OjXA31za3Ufp/iE3U/vM=", "owner": "vioken", "repo": "waylib", "rev": "12ff83a5066308de03542d2b0c106a3470076ba6", "type": "github"}, "original": {"owner": "vioken", "repo": "waylib", "type": "github"}}}, "root": "root", "version": 7}